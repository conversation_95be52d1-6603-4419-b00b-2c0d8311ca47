import { showToast } from 'vant';
import 'vant/es/toast/style';
import SectionHeader from '../../../components/SectionHeader.vue';
import { useSystemConfig } from '../../../composables/useSystemConfig';
// 使用系统配置
const { effectiveContactInfo } = useSystemConfig();
// 拨打电话
const makeCall = () => {
    const phoneNumber = effectiveContactInfo.value.phone;
    if (navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)) {
        window.location.href = `tel:${phoneNumber}`;
    }
    else {
        // 复制到剪贴板
        navigator.clipboard.writeText(phoneNumber).then(() => {
            showToast('电话号码已复制到剪贴板');
        }).catch(() => {
            showToast('复制失败，请手动拨打：' + phoneNumber);
        });
    }
};
// 发送邮件
const sendEmail = () => {
    const email = effectiveContactInfo.value.email;
    if (navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)) {
        window.location.href = `mailto:${email}`;
    }
    else {
        // 复制到剪贴板
        navigator.clipboard.writeText(email).then(() => {
            showToast('邮箱地址已复制到剪贴板');
        }).catch(() => {
            showToast('复制失败，邮箱：' + email);
        });
    }
};
// 打开地图
const openMap = () => {
    const address = effectiveContactInfo.value.address;
    // 尝试打开地图应用
    if (navigator.userAgent.match(/(iPhone|iPod|ios)/i)) {
        window.location.href = `maps://maps.apple.com/?q=${encodeURIComponent(address)}`;
    }
    else if (navigator.userAgent.match(/Android/i)) {
        window.location.href = `geo:0,0?q=${encodeURIComponent(address)}`;
    }
    else {
        // 复制地址到剪贴板
        navigator.clipboard.writeText(address).then(() => {
            showToast('地址已复制到剪贴板');
        }).catch(() => {
            showToast('地址：' + address);
        });
    }
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-label']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-value']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-value']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: "联系我们",
    icon: "phone-o",
    showMore: (false),
}));
const __VLS_1 = __VLS_0({
    title: "联系我们",
    icon: "phone-o",
    showMore: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-content" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-list" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ onClick: (__VLS_ctx.makeCall) },
    ...{ class: "contact-item" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-icon phone-icon" },
});
const __VLS_3 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
    name: "phone-o",
    size: "20",
}));
const __VLS_5 = __VLS_4({
    name: "phone-o",
    size: "20",
}, ...__VLS_functionalComponentArgsRest(__VLS_4));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-info" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-label" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-value" },
});
(__VLS_ctx.effectiveContactInfo.phone);
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ onClick: (__VLS_ctx.sendEmail) },
    ...{ class: "contact-item" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-icon email-icon" },
});
const __VLS_7 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
    name: "envelop-o",
    size: "20",
}));
const __VLS_9 = __VLS_8({
    name: "envelop-o",
    size: "20",
}, ...__VLS_functionalComponentArgsRest(__VLS_8));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-info" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-label" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-value" },
});
(__VLS_ctx.effectiveContactInfo.email);
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ onClick: (__VLS_ctx.openMap) },
    ...{ class: "contact-item" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-icon location-icon" },
});
const __VLS_11 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_12 = __VLS_asFunctionalComponent(__VLS_11, new __VLS_11({
    name: "location-o",
    size: "20",
}));
const __VLS_13 = __VLS_12({
    name: "location-o",
    size: "20",
}, ...__VLS_functionalComponentArgsRest(__VLS_12));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-info" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-label" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-value" },
});
(__VLS_ctx.effectiveContactInfo.address);
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-item" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-icon time-icon" },
});
const __VLS_15 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_16 = __VLS_asFunctionalComponent(__VLS_15, new __VLS_15({
    name: "clock-o",
    size: "20",
}));
const __VLS_17 = __VLS_16({
    name: "clock-o",
    size: "20",
}, ...__VLS_functionalComponentArgsRest(__VLS_16));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-info" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-label" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "contact-value" },
});
(__VLS_ctx.effectiveContactInfo.business_hours);
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-content']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-list']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['phone-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-info']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-label']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-value']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['email-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-info']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-label']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-value']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['location-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-info']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-label']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-value']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-item']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['time-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-info']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-label']} */ ;
/** @type {__VLS_StyleScopedClasses['contact-value']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            effectiveContactInfo: effectiveContactInfo,
            makeCall: makeCall,
            sendEmail: sendEmail,
            openMap: openMap,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
