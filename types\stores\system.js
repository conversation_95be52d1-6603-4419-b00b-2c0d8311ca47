import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getHospitalInfo, getContactInfo, getSeoConfig } from '../api/system';
export const useSystemStore = defineStore('system', () => {
    // 状态数据
    const hospitalInfo = ref(null);
    const contactInfo = ref(null);
    const seoConfig = ref(null);
    const loading = ref(false);
    const error = ref('');
    // 计算属性 - 版权年份范围
    const copyrightYearRange = computed(() => {
        if (!hospitalInfo.value)
            return '';
        if (!hospitalInfo.value.show_auto_year) {
            // 如果不自动计算，直接返回建院年份
            return hospitalInfo.value.establishment_year.toString();
        }
        const currentYear = new Date().getFullYear();
        const startYear = hospitalInfo.value.establishment_year;
        return startYear === currentYear
            ? `${startYear}`
            : `${startYear}-${currentYear}`;
    });
    // 完整的版权信息
    const fullCopyright = computed(() => {
        if (!hospitalInfo.value)
            return '';
        return `© ${copyrightYearRange.value} ${hospitalInfo.value.copyright_holder} ${hospitalInfo.value.copyright_text}`;
    });
    // 加载所有系统配置
    const loadSystemConfig = async () => {
        loading.value = true;
        error.value = '';
        try {
            const [hospitalRes, contactRes, seoRes] = await Promise.all([
                getHospitalInfo(),
                getContactInfo(),
                getSeoConfig()
            ]);
            if (hospitalRes.code === 200) {
                hospitalInfo.value = hospitalRes.data;
            }
            if (contactRes.code === 200) {
                contactInfo.value = contactRes.data;
            }
            if (seoRes.code === 200) {
                seoConfig.value = seoRes.data;
            }
        }
        catch (err) {
            error.value = '加载系统配置失败';
            console.error('系统配置加载错误:', err);
        }
        finally {
            loading.value = false;
        }
    };
    // 单独加载医院信息
    const loadHospitalInfo = async () => {
        try {
            const response = await getHospitalInfo();
            if (response.code === 200) {
                hospitalInfo.value = response.data;
            }
        }
        catch (err) {
            console.error('医院信息加载错误:', err);
        }
    };
    // 单独加载联系信息
    const loadContactInfo = async () => {
        try {
            const response = await getContactInfo();
            if (response.code === 200) {
                contactInfo.value = response.data;
            }
        }
        catch (err) {
            console.error('联系信息加载错误:', err);
        }
    };
    // 单独加载SEO配置
    const loadSeoConfig = async () => {
        try {
            const response = await getSeoConfig();
            if (response.code === 200) {
                seoConfig.value = response.data;
            }
        }
        catch (err) {
            console.error('SEO配置加载错误:', err);
        }
    };
    return {
        // 状态
        hospitalInfo,
        contactInfo,
        seoConfig,
        loading,
        error,
        // 计算属性
        copyrightYearRange,
        fullCopyright,
        // 方法
        loadSystemConfig,
        loadHospitalInfo,
        loadContactInfo,
        loadSeoConfig
    };
});
