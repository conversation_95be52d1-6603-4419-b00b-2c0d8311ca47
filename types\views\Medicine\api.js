import { get, requestWithRetry } from '../../api/request';
import { MEDICINE_URLS } from '../../api/urls';
/**
 * 获取领导分类
 * @returns 领导分类数据
 */
export function getMedicineCategories() {
    return get(MEDICINE_URLS.CATEGORIES);
}
/**
 * 根据分类获取领导列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 领导列表数据
 */
export function getMedicineByCategory(cat, page = 1, pageSize = 10) {
    return get(MEDICINE_URLS.MEDICINE, {
        cat,
        page,
        page_size: pageSize
    });
}
/**
 * 带重试功能的获取领导分类
 * 在网络不稳定情况下使用
 * @returns 领导分类数据
 */
export function getMedicineCategoriesWithRetry(cat, page = 1, pageSize = 10) {
    return requestWithRetry(() => getMedicineByCategory(cat, page, pageSize));
}
