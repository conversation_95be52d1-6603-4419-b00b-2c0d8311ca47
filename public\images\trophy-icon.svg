<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="trophyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700"/>
      <stop offset="50%" style="stop-color:#ffed4e"/>
      <stop offset="100%" style="stop-color:#ff9500"/>
    </linearGradient>
    <linearGradient id="baseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#888"/>
      <stop offset="100%" style="stop-color:#555"/>
    </linearGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#ffd700;stop-opacity:0"/>
    </radialGradient>
  </defs>
  
  <!-- 光晕效果 -->
  <circle cx="32" cy="32" r="30" fill="url(#glowGradient)"/>
  
  <!-- 奖杯底座 -->
  <rect x="20" y="48" width="24" height="8" rx="2" fill="url(#baseGradient)"/>
  <rect x="24" y="44" width="16" height="4" rx="1" fill="url(#baseGradient)"/>
  
  <!-- 奖杯主体 -->
  <path d="M24 44 L24 28 Q24 20 32 20 Q40 20 40 28 L40 44 Z" fill="url(#trophyGradient)" stroke="#ff9500" stroke-width="1"/>
  
  <!-- 奖杯把手 -->
  <path d="M20 24 Q16 24 16 28 Q16 32 20 32" stroke="url(#trophyGradient)" stroke-width="3" fill="none"/>
  <path d="M44 24 Q48 24 48 28 Q48 32 44 32" stroke="url(#trophyGradient)" stroke-width="3" fill="none"/>
  
  <!-- 奖杯装饰 -->
  <ellipse cx="32" cy="30" rx="6" ry="8" fill="none" stroke="#ff9500" stroke-width="1" opacity="0.5"/>
  
  <!-- 皇冠装饰 -->
  <g transform="translate(32, 16)">
    <path d="M-8,0 L-4,-4 L0,0 L4,-4 L8,0 L6,4 L-6,4 Z" fill="url(#trophyGradient)" stroke="#ff9500" stroke-width="1"/>
    <circle cx="-4" cy="-2" r="1" fill="#ff6b00"/>
    <circle cx="0" cy="-2" r="1.5" fill="#ff6b00"/>
    <circle cx="4" cy="-2" r="1" fill="#ff6b00"/>
  </g>
  
  <!-- 闪光效果 -->
  <g transform="translate(28, 24) scale(0.5)" opacity="0.8">
    <path d="M0,-6 L2,0 L6,0 L3,3 L4,8 L0,5 L-4,8 L-3,3 L-6,0 L-2,0 Z" fill="white"/>
  </g>
  
  <g transform="translate(36, 32) scale(0.3)" opacity="0.6">
    <path d="M0,-6 L2,0 L6,0 L3,3 L4,8 L0,5 L-4,8 L-3,3 L-6,0 L-2,0 Z" fill="white"/>
  </g>
  
  <!-- 数字1装饰 -->
  <text x="32" y="36" text-anchor="middle" font-family="Arial Black" font-size="12" font-weight="bold" fill="#ff6b00">1</text>
</svg>
