import { computed } from 'vue';
import { useSystemConfig } from '../composables/useSystemConfig';
// 可自定义传入品牌/口号/icon
const props = defineProps({
    brand: {
        type: String,
        default: '', // 空字符串表示使用配置的默认值
    },
    slogan: {
        type: String,
        default: '', // 空字符串表示使用配置的默认值
    },
    icon: {
        type: String,
        default: 'medic-o', // VanIcon 中图标，保持默认值
    },
    startYear: {
        type: Number,
        default: 0, // 0表示使用配置的默认年份
    }
});
// 使用系统配置
const { getHospitalName, getHospitalSlogan, getCopyrightText, effectiveHospitalInfo } = useSystemConfig();
// 计算有效的品牌名称
const effectiveBrand = computed(() => {
    return props.brand || getHospitalName();
});
// 计算有效的口号
const effectiveSlogan = computed(() => {
    return props.slogan || getHospitalSlogan();
});
// 计算有效的图标
const effectiveIcon = computed(() => {
    return props.icon; // 图标保持使用props的值
});
// 计算版权文本
const copyrightText = computed(() => {
    if (props.startYear > 0) {
        // 如果props提供了年份，使用props的逻辑
        const currentYear = new Date().getFullYear();
        const yearRange = props.startYear === currentYear
            ? `${props.startYear}`
            : `${props.startYear}-${currentYear}`;
        const brandName = effectiveBrand.value;
        return `© ${yearRange} ${brandName} 版权所有`;
    }
    else {
        // 使用系统配置的版权文本
        return getCopyrightText();
    }
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "global-footer" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "footer-content" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "footer-logo" },
});
const __VLS_0 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    name: (__VLS_ctx.effectiveIcon),
    size: "22",
    color: "#2b72f6",
}));
const __VLS_2 = __VLS_1({
    name: (__VLS_ctx.effectiveIcon),
    size: "22",
    color: "#2b72f6",
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
    ...{ class: "brand-name" },
});
(__VLS_ctx.effectiveBrand);
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
    ...{ class: "footer-slogan" },
});
(__VLS_ctx.effectiveSlogan);
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
    ...{ class: "footer-copy" },
});
(__VLS_ctx.copyrightText);
/** @type {__VLS_StyleScopedClasses['global-footer']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-content']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-logo']} */ ;
/** @type {__VLS_StyleScopedClasses['brand-name']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-slogan']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-copy']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            effectiveBrand: effectiveBrand,
            effectiveSlogan: effectiveSlogan,
            effectiveIcon: effectiveIcon,
            copyrightText: copyrightText,
        };
    },
    props: {
        brand: {
            type: String,
            default: '', // 空字符串表示使用配置的默认值
        },
        slogan: {
            type: String,
            default: '', // 空字符串表示使用配置的默认值
        },
        icon: {
            type: String,
            default: 'medic-o', // VanIcon 中图标，保持默认值
        },
        startYear: {
            type: Number,
            default: 0, // 0表示使用配置的默认年份
        }
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    props: {
        brand: {
            type: String,
            default: '', // 空字符串表示使用配置的默认值
        },
        slogan: {
            type: String,
            default: '', // 空字符串表示使用配置的默认值
        },
        icon: {
            type: String,
            default: 'medic-o', // VanIcon 中图标，保持默认值
        },
        startYear: {
            type: Number,
            default: 0, // 0表示使用配置的默认年份
        }
    },
});
; /* PartiallyEnd: #4569/main.vue */
