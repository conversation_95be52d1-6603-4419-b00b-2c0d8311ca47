import { ref, watch, onMounted, nextTick } from 'vue';
import { formatDateTime, formatActivityTimeRange } from '../utils/dateTime';
const scrollContainerRef = ref(null);
const getScrollContainer = () => scrollContainerRef.value;
onMounted(() => {
    nextTick(() => {
        console.log('ActivityHistoryCardList 滚动容器:', scrollContainerRef.value);
    });
});
const props = withDefaults(defineProps(), {
    emptyText: '暂无活动历史',
    useInfiniteScroll: true,
    finished: false,
    activityType: 'past', // 默认为历史活动
});
const emit = defineEmits(['load-more', 'card-click']);
const internalLoading = ref(false);
watch(() => props.loading, (val) => {
    internalLoading.value = val;
}, { immediate: true });
const onLoadMore = () => {
    console.log('ActivityHistoryCardList 触发加载更多');
    emit('load-more');
};
// 格式化活动时间
const formatActivityTime = (item) => {
    // 优先显示开始时间，使用格式化函数
    if (item.startdate) {
        // 如果有结束时间，显示时间范围
        if (item.enddate) {
            return formatActivityTimeRange(item.startdate, item.enddate);
        }
        return formatDateTime(item.startdate);
    }
    else if (item.start_time) {
        if (item.end_time) {
            return formatActivityTimeRange(item.start_time, item.end_time);
        }
        return formatDateTime(item.start_time);
    }
    else if (item.activity_date) {
        return formatDateTime(item.activity_date);
    }
    else if (item.create_time) {
        return formatDateTime(item.create_time);
    }
    return '时间待定';
};
// 获取活动状态
const getActivityStatus = (item) => {
    const now = new Date();
    // 如果有明确的状态，优先使用
    if (item.status) {
        return item.status;
    }
    // 根据活动类型显示对应的标签
    switch (props.activityType) {
        case 'upcoming':
            return '活动预告';
        case 'current':
            return '本月活动';
        case 'past':
            return '历史活动';
        default:
            // 对于其他情况，根据时间判断活动状态
            if (item.startdate && item.enddate) {
                const startDate = new Date(item.startdate);
                const endDate = new Date(item.enddate);
                if (now < startDate) {
                    return '未开始';
                }
                else if (now > endDate) {
                    return '已结束';
                }
                else {
                    return '进行中';
                }
            }
            return '已结束';
    }
};
// 获取状态标签类型
const getStatusTagType = (item) => {
    const status = getActivityStatus(item);
    switch (status) {
        case '进行中':
            return 'success';
        case '未开始':
            return 'primary';
        case '已结束':
            return 'default';
        case '已取消':
            return 'danger';
        case '活动预告':
            return 'primary'; // 蓝色标签，表示即将到来
        case '本月活动':
            return 'success'; // 绿色标签，表示当前活跃
        case '历史活动':
            return 'default'; // 灰色标签，表示已过去
        default:
            return 'default';
    }
};
// 检查是否有元信息
const hasMetaInfo = (item) => {
    return !!(item.location || item.address || item.participants || item.current_participants || item.cat_display || item.category);
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    emptyText: '暂无活动历史',
    useInfiniteScroll: true,
    finished: false,
    activityType: 'past', // 默认为历史活动
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['history-card']} */ ;
/** @type {__VLS_StyleScopedClasses['history-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-history-card-list']} */ ;
/** @type {__VLS_StyleScopedClasses['history-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['time-content']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['number-circle']} */ ;
/** @type {__VLS_StyleScopedClasses['history-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-history-card-list" },
    ref: "scrollContainerRef",
});
/** @type {typeof __VLS_ctx.scrollContainerRef} */ ;
if (__VLS_ctx.loading && !__VLS_ctx.items.length) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_0 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        size: "24px",
    }));
    const __VLS_2 = __VLS_1({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_3.slots.default;
    var __VLS_3;
}
else if (!__VLS_ctx.items.length) {
    const __VLS_4 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        description: (__VLS_ctx.emptyText),
    }));
    const __VLS_6 = __VLS_5({
        description: (__VLS_ctx.emptyText),
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
}
else if (__VLS_ctx.useInfiniteScroll) {
    const __VLS_8 = {}.VanList;
    /** @type {[typeof __VLS_components.VanList, typeof __VLS_components.vanList, typeof __VLS_components.VanList, typeof __VLS_components.vanList, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }));
    const __VLS_10 = __VLS_9({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    let __VLS_12;
    let __VLS_13;
    let __VLS_14;
    const __VLS_15 = {
        onLoad: (__VLS_ctx.onLoadMore)
    };
    __VLS_11.slots.default;
    for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "history-card animate__animated animate__fadeInUp" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-header" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-number" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "number-circle" },
        });
        (index + 1);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-time" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "time-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "time-content" },
        });
        (__VLS_ctx.formatActivityTime(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-image-container" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail || item.image),
            alt: (item.name || item.title),
            ...{ class: "activity-image" },
        });
        if (__VLS_ctx.getActivityStatus(item)) {
            const __VLS_16 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "status-tag" },
                round: true,
            }));
            const __VLS_18 = __VLS_17({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "status-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_17));
            __VLS_19.slots.default;
            (__VLS_ctx.getActivityStatus(item));
            var __VLS_19;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-text" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "activity-title" },
        });
        (item.name || item.title);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "activity-description" },
        });
        (item.desc || item.description || item.summary);
        if (__VLS_ctx.hasMetaInfo(item)) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-meta" },
            });
            if (item.location || item.address) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                    ...{ class: "meta-item" },
                });
                const __VLS_20 = {}.VanIcon;
                /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
                // @ts-ignore
                const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
                    name: "location-o",
                }));
                const __VLS_22 = __VLS_21({
                    name: "location-o",
                }, ...__VLS_functionalComponentArgsRest(__VLS_21));
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
                (item.location || item.address);
            }
            if (item.participants || item.current_participants) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                    ...{ class: "meta-item" },
                });
                const __VLS_24 = {}.VanIcon;
                /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
                // @ts-ignore
                const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
                    name: "friends-o",
                }));
                const __VLS_26 = __VLS_25({
                    name: "friends-o",
                }, ...__VLS_functionalComponentArgsRest(__VLS_25));
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
                (item.participants || item.current_participants);
            }
            if (item.cat_display || item.category) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                    ...{ class: "meta-item" },
                });
                const __VLS_28 = {}.VanIcon;
                /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
                // @ts-ignore
                const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
                    name: "label-o",
                }));
                const __VLS_30 = __VLS_29({
                    name: "label-o",
                }, ...__VLS_functionalComponentArgsRest(__VLS_29));
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
                (item.cat_display || item.category);
            }
        }
    }
    var __VLS_11;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({});
    for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "history-card animate__animated animate__fadeInUp" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-header" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-number" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "number-circle" },
        });
        (index + 1);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-time" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "time-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "time-content" },
        });
        (__VLS_ctx.formatActivityTime(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-image-container" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail || item.image),
            alt: (item.name || item.title),
            ...{ class: "activity-image" },
        });
        if (__VLS_ctx.getActivityStatus(item)) {
            const __VLS_32 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "status-tag" },
                round: true,
            }));
            const __VLS_34 = __VLS_33({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "status-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_33));
            __VLS_35.slots.default;
            (__VLS_ctx.getActivityStatus(item));
            var __VLS_35;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-text" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "activity-title" },
        });
        (item.name || item.title);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "activity-description" },
        });
        (item.desc || item.description || item.summary);
        if (__VLS_ctx.hasMetaInfo(item)) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-meta" },
            });
            if (item.location || item.address) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                    ...{ class: "meta-item" },
                });
                const __VLS_36 = {}.VanIcon;
                /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
                // @ts-ignore
                const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
                    name: "location-o",
                }));
                const __VLS_38 = __VLS_37({
                    name: "location-o",
                }, ...__VLS_functionalComponentArgsRest(__VLS_37));
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
                (item.location || item.address);
            }
            if (item.participants || item.current_participants) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                    ...{ class: "meta-item" },
                });
                const __VLS_40 = {}.VanIcon;
                /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
                // @ts-ignore
                const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
                    name: "friends-o",
                }));
                const __VLS_42 = __VLS_41({
                    name: "friends-o",
                }, ...__VLS_functionalComponentArgsRest(__VLS_41));
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
                (item.participants || item.current_participants);
            }
            if (item.cat_display || item.category) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                    ...{ class: "meta-item" },
                });
                const __VLS_44 = {}.VanIcon;
                /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
                // @ts-ignore
                const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
                    name: "label-o",
                }));
                const __VLS_46 = __VLS_45({
                    name: "label-o",
                }, ...__VLS_functionalComponentArgsRest(__VLS_45));
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
                (item.cat_display || item.category);
            }
        }
    }
}
/** @type {__VLS_StyleScopedClasses['activity-history-card-list']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['history-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-header']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-number']} */ ;
/** @type {__VLS_StyleScopedClasses['number-circle']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-time']} */ ;
/** @type {__VLS_StyleScopedClasses['time-label']} */ ;
/** @type {__VLS_StyleScopedClasses['time-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['status-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-text']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['history-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-header']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-number']} */ ;
/** @type {__VLS_StyleScopedClasses['number-circle']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-time']} */ ;
/** @type {__VLS_StyleScopedClasses['time-label']} */ ;
/** @type {__VLS_StyleScopedClasses['time-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['status-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-text']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            scrollContainerRef: scrollContainerRef,
            getScrollContainer: getScrollContainer,
            internalLoading: internalLoading,
            onLoadMore: onLoadMore,
            formatActivityTime: formatActivityTime,
            getActivityStatus: getActivityStatus,
            getStatusTagType: getStatusTagType,
            hasMetaInfo: hasMetaInfo,
        };
    },
    emits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
