#!/bin/bash

# SSL证书安装脚本 - 使用Let's Encrypt免费证书
echo "🔒 安装SSL证书 - Let's Encrypt"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用sudo运行此脚本${NC}"
    exit 1
fi

# 获取域名
echo -e "${YELLOW}请输入您的域名 (例如: www.example.com):${NC}"
read -p "域名: " DOMAIN

if [ -z "$DOMAIN" ]; then
    echo -e "${RED}❌ 域名不能为空${NC}"
    exit 1
fi

echo -e "${BLUE}步骤1: 安装Certbot${NC}"
apt update
apt install -y certbot python3-certbot-nginx

echo -e "${BLUE}步骤2: 申请SSL证书${NC}"
certbot --nginx -d "$DOMAIN" --non-interactive --agree-tos --email admin@"$DOMAIN"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ SSL证书申请成功${NC}"
    
    echo -e "${BLUE}步骤3: 配置自动续期${NC}"
    # 添加自动续期定时任务
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
    
    echo -e "${GREEN}✅ 自动续期配置完成${NC}"
    
    echo -e "${BLUE}步骤4: 测试Nginx配置${NC}"
    nginx -t && systemctl reload nginx
    
    echo ""
    echo -e "${GREEN}🎉 SSL证书安装完成！${NC}"
    echo -e "${YELLOW}您的网站现在可以通过HTTPS访问:${NC}"
    echo -e "https://$DOMAIN"
    
else
    echo -e "${RED}❌ SSL证书申请失败${NC}"
    echo -e "${YELLOW}可能的原因:${NC}"
    echo "1. 域名未正确解析到服务器"
    echo "2. 防火墙阻止了80/443端口"
    echo "3. Nginx配置错误"
    exit 1
fi 