<template>
  <div class="global-footer">
    <div class="footer-content">
      <div class="footer-logo">
        <van-icon :name="effectiveIcon" size="22" color="#2b72f6" />
        <span class="brand-name">{{ effectiveBrand }}</span>
      </div>
      <p class="footer-slogan">{{ effectiveSlogan }}</p>
      <p class="footer-copy">{{ copyrightText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useSystemConfig } from '../composables/useSystemConfig';

// 可自定义传入品牌/口号/icon
const props = defineProps({
  brand: {
    type: String,
    default: '',  // 空字符串表示使用配置的默认值
  },
  slogan: {
    type: String,
    default: '',  // 空字符串表示使用配置的默认值
  },
  icon: {
    type: String,
    default: 'medic-o', // VanIcon 中图标，保持默认值
  },
  startYear: {
    type: Number,
    default: 0,  // 0表示使用配置的默认年份
  }
})

// 使用系统配置
const { 
  getHospitalName, 
  getHospitalSlogan, 
  getCopyrightText, 
  effectiveHospitalInfo 
} = useSystemConfig();

// 计算有效的品牌名称
const effectiveBrand = computed(() => {
  return props.brand || getHospitalName();
});

// 计算有效的口号
const effectiveSlogan = computed(() => {
  return props.slogan || getHospitalSlogan();
});

// 计算有效的图标
const effectiveIcon = computed(() => {
  return props.icon; // 图标保持使用props的值
});

// 计算版权文本
const copyrightText = computed(() => {
  if (props.startYear > 0) {
    // 如果props提供了年份，使用props的逻辑
    const currentYear = new Date().getFullYear();
    const yearRange = props.startYear === currentYear
      ? `${props.startYear}`
      : `${props.startYear}-${currentYear}`;
    const brandName = effectiveBrand.value;
    return `© ${yearRange} ${brandName} 版权所有`;
  } else {
    // 使用系统配置的版权文本
    return getCopyrightText();
  }
});
</script>

<style scoped>
.global-footer {
  background: #e8f1fd; /* 医疗蓝色基调 */
  color: #2c3e50;
  text-align: center;
  padding: 20px 16px 16px;
  border-top: 1px solid #d0dff7;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  position: relative;
  z-index: 1;
}

.footer-content {
  max-width: 360px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.brand-name {
  font-size: 16px;
  font-weight: 600;
  color: #2b72f6; /* 医疗蓝 */
}

.footer-slogan {
  font-size: 13px;
  color: #4a6572;
  margin: 0;
}

.footer-copy {
  font-size: 12px;
  color: #7b8d9e;
  margin: 0;
}
</style>
