<template>
  <div class="section-container contact-section">
    <SectionHeader title="联系我们" icon="phone-o"  :showMore="false" />
    <div class="contact-info animate__animated animate__fadeInUp">
      <div class="contact-item">
        <van-icon name="phone-o" color="#4b8bf4" size="16" />
        <span>电话: {{ effectiveContactInfo.phone }}</span>
      </div>
      <div class="contact-item">
        <van-icon name="envelop-o" color="#4b8bf4" size="16" />
        <span>邮箱: {{ effectiveContactInfo.email }}</span>
      </div>
      <div class="contact-item">
        <van-icon name="location-o" color="#4b8bf4" size="16" />
        <span>地址: {{ effectiveContactInfo.address }}</span>
      </div>
      <div class="contact-item">
        <van-icon name="clock-o" color="#4b8bf4" size="16" />
        <span>营业时间: {{ effectiveContactInfo.business_hours }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件
import { useSystemConfig } from '../../../composables/useSystemConfig';

// 使用系统配置
const { effectiveContactInfo } = useSystemConfig();
</script>

<style scoped>
/* 区块通用样式 */
.section-container {
  padding: 5px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #323233;
}

.section-title span {
  margin-left: 8px;
}

/* 订阅功能样式 */
.contact-section {
  margin-top: 20px;
}

.contact-info {
  padding: 10px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  margin: 12px 0;
  font-size: 14px;
  color: #333;
}

.contact-item .van-icon {
  margin-right: 10px;
  flex-shrink: 0;
}

.contact-item span {
  line-height: 1.5;
}

@media (max-width: 480px) {
  .contact-item {
    font-size: 13px;
  }
}
</style>
