import { ref, watch, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import { getActivityDetail } from '../views/Activity/api';
import { formatActivityTimeRange } from '../utils/dateTime';
// Props
const props = defineProps({
    activityId: {
        type: [String, Number],
        required: true
    },
    showActions: {
        type: Boolean,
        default: true
    }
});
// Emits
const emit = defineEmits(['loaded', 'error', 'register']);
const router = useRouter();
const loading = ref(false);
const error = ref('');
const submitting = ref(false);
// 活动数据
const activity = ref({
    id: 0,
    name: '',
    cat: '',
    cat_name: '',
    desc: '',
    thumbnail: '',
    isfree: 0,
    startdate: '',
    enddate: '',
    views: 0,
    location: '',
    content: '',
});
// 获取活动详情
const fetchActivityDetail = async () => {
    if (!props.activityId) {
        error.value = '活动ID无效';
        emit('error', '活动ID无效');
        return;
    }
    try {
        loading.value = true;
        error.value = '';
        const res = await getActivityDetail(props.activityId.toString());
        const data = res.activity;
        activity.value = {
            id: data.id,
            name: data.name,
            cat: data.cat,
            cat_name: data.cat_name,
            desc: data.desc,
            startdate: data.startdate,
            enddate: data.enddate,
            location: data.location,
            thumbnail: data.thumbnail,
            views: data.views,
            isfree: data.isfree,
            content: data.content || '',
        };
        emit('loaded', activity.value);
    }
    catch (err) {
        console.error('获取活动详情失败:', err);
        error.value = '获取活动详情失败，请稍后再试';
        emit('error', error.value);
    }
    finally {
        loading.value = false;
    }
};
// 处理报名
const handleRegister = async () => {
    const activityId = activity.value.id || props.activityId;
    const targetUrl = `/activity-registration/${activityId}`;
    try {
        await router.push(targetUrl);
        emit('register', activity.value);
    }
    catch (error) {
        console.error('跳转失败:', error);
        window.location.hash = `#${targetUrl}`;
    }
};
// 监听活动ID变化
watch(() => props.activityId, (newId) => {
    if (newId) {
        fetchActivityDetail();
    }
}, { immediate: true });
// 暴露刷新方法
const refresh = () => {
    fetchActivityDetail();
};
const __VLS_exposed = {
    refresh,
    activity
};
defineExpose(__VLS_exposed);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['info-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['info-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['register-btn']} */ ;
/** @type {__VLS_StyleScopedClasses['register-btn']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-hero']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-theme-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-container']} */ ;
/** @type {__VLS_StyleScopedClasses['register-section']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['section-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-theme-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-container']} */ ;
/** @type {__VLS_StyleScopedClasses['register-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-theme-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-container']} */ ;
/** @type {__VLS_StyleScopedClasses['register-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-hero']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-theme-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-container']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['register-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-hero']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-theme-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-container']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['register-section']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-detail-card" },
});
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_0 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        type: "spinner",
        color: "#4b8bf4",
        size: "32px",
    }));
    const __VLS_2 = __VLS_1({
        type: "spinner",
        color: "#4b8bf4",
        size: "32px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
}
else if (__VLS_ctx.error) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "error-container" },
    });
    const __VLS_4 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        description: "加载失败",
        image: "error",
    }));
    const __VLS_6 = __VLS_5({
        description: "加载失败",
        image: "error",
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
    __VLS_7.slots.default;
    {
        const { description: __VLS_thisSlot } = __VLS_7.slots;
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
        (__VLS_ctx.error);
    }
    const __VLS_8 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
    }));
    const __VLS_10 = __VLS_9({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    let __VLS_12;
    let __VLS_13;
    let __VLS_14;
    const __VLS_15 = {
        onClick: (__VLS_ctx.fetchActivityDetail)
    };
    __VLS_11.slots.default;
    var __VLS_11;
    var __VLS_7;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-hero" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
        src: (__VLS_ctx.activity.thumbnail),
        alt: (__VLS_ctx.activity.name),
        ...{ class: "hero-image" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-theme-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h1, __VLS_intrinsicElements.h1)({
        ...{ class: "activity-title" },
    });
    (__VLS_ctx.activity.name);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-subtitle" },
    });
    (__VLS_ctx.activity.desc);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-info-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-icon time-icon" },
    });
    const __VLS_16 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
        name: "clock-o",
    }));
    const __VLS_18 = __VLS_17({
        name: "clock-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_17));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-value" },
    });
    (__VLS_ctx.formatActivityTimeRange(__VLS_ctx.activity.startdate, __VLS_ctx.activity.enddate));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-icon location-icon" },
    });
    const __VLS_20 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
        name: "location-o",
    }));
    const __VLS_22 = __VLS_21({
        name: "location-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_21));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-value" },
    });
    (__VLS_ctx.activity.location || '待定');
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content-sections" },
    });
    if (__VLS_ctx.activity.content) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "content-section" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-header" },
        });
        const __VLS_24 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
            name: "notes-o",
            color: "#4b8bf4",
        }));
        const __VLS_26 = __VLS_25({
            name: "notes-o",
            color: "#4b8bf4",
        }, ...__VLS_functionalComponentArgsRest(__VLS_25));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({});
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "rich-content" },
        });
        __VLS_asFunctionalDirective(__VLS_directives.vHtml)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.activity.content) }, null, null);
    }
    if (!__VLS_ctx.loading && !__VLS_ctx.error && __VLS_ctx.showActions) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "register-section" },
        });
        const __VLS_28 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
            ...{ 'onClick': {} },
            ...{ class: "register-btn" },
            type: "primary",
            loading: (__VLS_ctx.submitting),
            block: true,
            round: true,
        }));
        const __VLS_30 = __VLS_29({
            ...{ 'onClick': {} },
            ...{ class: "register-btn" },
            type: "primary",
            loading: (__VLS_ctx.submitting),
            block: true,
            round: true,
        }, ...__VLS_functionalComponentArgsRest(__VLS_29));
        let __VLS_32;
        let __VLS_33;
        let __VLS_34;
        const __VLS_35 = {
            onClick: (__VLS_ctx.handleRegister)
        };
        __VLS_31.slots.default;
        var __VLS_31;
    }
}
if (false) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ style: {} },
    });
    (__VLS_ctx.loading);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.br, __VLS_intrinsicElements.br)({});
    (__VLS_ctx.error);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.br, __VLS_intrinsicElements.br)({});
    (__VLS_ctx.showActions);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.br, __VLS_intrinsicElements.br)({});
    (__VLS_ctx.activityId);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.br, __VLS_intrinsicElements.br)({});
    (!__VLS_ctx.loading && !__VLS_ctx.error && __VLS_ctx.showActions);
}
/** @type {__VLS_StyleScopedClasses['activity-detail-card']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['error-container']} */ ;
/** @type {__VLS_StyleScopedClasses['content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-hero']} */ ;
/** @type {__VLS_StyleScopedClasses['hero-image']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-theme-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-subtitle']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-container']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['info-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['time-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['info-content']} */ ;
/** @type {__VLS_StyleScopedClasses['info-label']} */ ;
/** @type {__VLS_StyleScopedClasses['info-value']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['info-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['location-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['info-content']} */ ;
/** @type {__VLS_StyleScopedClasses['info-label']} */ ;
/** @type {__VLS_StyleScopedClasses['info-value']} */ ;
/** @type {__VLS_StyleScopedClasses['content-sections']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['register-section']} */ ;
/** @type {__VLS_StyleScopedClasses['register-btn']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            formatActivityTimeRange: formatActivityTimeRange,
            loading: loading,
            error: error,
            submitting: submitting,
            activity: activity,
            fetchActivityDetail: fetchActivityDetail,
            handleRegister: handleRegister,
        };
    },
    emits: {},
    props: {
        activityId: {
            type: [String, Number],
            required: true
        },
        showActions: {
            type: Boolean,
            default: true
        }
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {
            ...__VLS_exposed,
        };
    },
    emits: {},
    props: {
        activityId: {
            type: [String, Number],
            required: true
        },
        showActions: {
            type: Boolean,
            default: true
        }
    },
});
; /* PartiallyEnd: #4569/main.vue */
