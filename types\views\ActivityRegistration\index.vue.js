import { ref, onMounted, computed, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import { getActivityDetail } from '../Activity/api';
import { postActivityRegistration, postSMSCodeSend } from './api';
import { formatActivityTimeRange, formatDateTime } from '../../utils/dateTime';
import 'vant/es/toast/style'; // ✅ 必须引入 Toast 样式
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const error = ref('');
const submitting = ref(false);
const showAgreementPopup = ref(false);
const formRef = ref();
// 短信验证相关
const sendingSms = ref(false);
const smsCountdown = ref(0);
const smsTimer = ref(null);
// 报名成功状态
const registrationSuccess = ref(false);
const registrationInfo = ref({
    id: 0,
    regno: '',
    name: '',
    phone: '',
    age: '',
    create_time: '',
    qrcode: ''
});
// 表单数据
const formData = ref({
    name: '',
    phone: '',
    verificationCode: '',
    age: '',
    agreeTerms: false
});
const goBack = () => {
    router.back();
};
// 活动数据
const activity = ref({
    id: 0,
    name: '',
    cat: '',
    cat_name: '',
    desc: '',
    thumbnail: '',
    isfree: 0,
    startdate: '',
    enddate: '',
    views: 0,
    location: '',
    content: '',
});
// 获取活动详情
const fetchActivityDetail = async () => {
    const id = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
    console.log('获取活动详情，ID:', id);
    if (!id) {
        error.value = '活动ID无效';
        return;
    }
    try {
        loading.value = true;
        const res = await getActivityDetail(id);
        console.log('获取到活动详情数据:', res);
        const data = res.activity;
        activity.value = {
            id: data.id,
            name: data.name,
            cat: data.cat,
            cat_name: data.cat_name,
            desc: data.desc,
            startdate: data.startdate,
            enddate: data.enddate,
            location: data.location,
            thumbnail: data.thumbnail,
            views: data.views,
            isfree: data.isfree,
            content: data.content || '',
        };
    }
    catch (err) {
        console.error('获取活动详情失败:', err);
        error.value = '获取活动详情失败，请稍后再试';
    }
    finally {
        loading.value = false;
    }
};
// 判断是否可以发送短信
const canSendSms = computed(() => {
    return /^1[3-9]\d{9}$/.test(formData.value.phone);
});
// 获取短信按钮文本
const getSmsButtonText = () => {
    if (smsCountdown.value > 0) {
        return `${smsCountdown.value}s`;
    }
    return sendingSms.value ? '发送中' : '获取验证码';
};
// 发送短信验证码
const sendSmsCode = async () => {
    if (!canSendSms.value) {
        showToast({ type: 'fail', message: '请输入正确的手机号' });
        return;
    }
    try {
        sendingSms.value = true;
        // 调用真实 API - 发送验证码
        console.log('发送验证码到手机号:', formData.value.phone);
        const res = await postSMSCodeSend({ phone: formData.value.phone });
        if ('message' in res) {
            showToast({ type: 'success', message: res.message });
        }
        else if ('error' in res) {
            showToast({ type: 'fail', message: res.error });
            return; // 不启动倒计时
        }
        // ✅ 成功后启动倒计时
        smsCountdown.value = 60;
        smsTimer.value = setInterval(() => {
            smsCountdown.value--;
            if (smsCountdown.value <= 0) {
                clearInterval(smsTimer.value);
                smsTimer.value = null;
            }
        }, 1000);
    }
    catch (err) {
        console.error('发送短信失败:', err);
        showToast({ type: 'fail', message: '发送失败，请稍后再试' });
    }
    finally {
        sendingSms.value = false;
    }
};
// 显示协议
const showAgreement = () => {
    showAgreementPopup.value = true;
};
// 同意协议并关闭
const agreeAndClose = () => {
    formData.value.agreeTerms = true;
    showAgreementPopup.value = false;
    showToast({ type: 'success', message: '已同意协议' });
};
// 表单提交
const onSubmit = async (values) => {
    submitting.value = true;
    console.log('提交报名信息:', values);
    try {
        // 表单校验
        await formRef.value?.validate();
        // 验证手机号和验证码是否填写
        if (!formData.value.phone || !values.verificationCode) {
            showToast({ type: 'fail', message: '手机号和验证码不能为空' });
            return;
        }
        // 构造请求参数 - 按照后端要求的字段名传递数据
        const requestParams = {
            activity: activity.value.id,
            name: values.name,
            phone: formData.value.phone, // 使用formData中的phone值，映射到phone字段
            code: values.verificationCode, // 后端需要 code 字段用于验证码校验
            age: values.age,
        };
        console.log('发送报名请求参数:', requestParams);
        // 请求后端接口
        const res = await postActivityRegistration(requestParams);
        console.log('报名接口返回:', res);
        // 检查是否有错误响应
        if ('success' in res && res.success === false) {
            // 处理后端返回的错误信息
            let errorMessage = res.message || '报名失败';
            // 如果有详细的验证错误信息，显示具体错误
            if (res.errors) {
                const errorMessages = Object.entries(res.errors)
                    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
                    .join('; ');
                errorMessage = errorMessages || errorMessage;
            }
            showToast({ type: 'fail', message: errorMessage });
            return;
        }
        // 获取成功响应的数据
        const reg = 'results' in res ? res.results : null;
        if (!reg) {
            throw new Error('报名失败：返回数据格式错误');
        }
        // 成功：赋值信息 + 切换视图
        registrationInfo.value = {
            id: reg.id || 0,
            regno: reg.regno || '',
            name: reg.name || '',
            phone: reg.phone || '', // 使用后端返回的phone字段
            age: reg.age || '',
            create_time: reg.create_time || '',
            qrcode: reg.qrcode || '',
        };
        registrationSuccess.value = true;
        showToast({ type: 'success', message: '报名成功！' });
        console.log('报名成功:', reg);
    }
    catch (err) {
        console.error('报名失败:', err);
        // 处理网络错误或其他异常
        let errorMessage = '报名失败，请稍后再试';
        if (err?.response?.data) {
            const errorData = err.response.data;
            if (errorData.message) {
                errorMessage = errorData.message;
            }
            else if (errorData.errors) {
                const errorMessages = Object.values(errorData.errors).flat().join(', ');
                errorMessage = errorMessages;
            }
        }
        else if (err?.message) {
            errorMessage = err.message;
        }
        showToast({ type: 'fail', message: errorMessage });
    }
    finally {
        submitting.value = false;
    }
};
// 清理定时器
const cleanup = () => {
    if (smsTimer.value) {
        clearInterval(smsTimer.value);
        smsTimer.value = null;
    }
};
// 初始化
onMounted(() => {
    fetchActivityDetail();
});
// 组件卸载时清理
onUnmounted(() => {
    cleanup();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-registration-page" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "custom-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "header-content" },
});
const __VLS_0 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    ...{ 'onClick': {} },
    name: "arrow-left",
    ...{ class: "back-icon" },
}));
const __VLS_2 = __VLS_1({
    ...{ 'onClick': {} },
    name: "arrow-left",
    ...{ class: "back-icon" },
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
let __VLS_4;
let __VLS_5;
let __VLS_6;
const __VLS_7 = {
    onClick: (__VLS_ctx.goBack)
};
var __VLS_3;
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
    ...{ class: "header-title" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "header-placeholder" },
});
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_8 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        type: "spinner",
        color: "#4b8bf4",
        size: "32px",
    }));
    const __VLS_10 = __VLS_9({
        type: "spinner",
        color: "#4b8bf4",
        size: "32px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
}
else if (__VLS_ctx.error) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "error-container" },
    });
    const __VLS_12 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({
        description: "加载失败",
        image: "error",
    }));
    const __VLS_14 = __VLS_13({
        description: "加载失败",
        image: "error",
    }, ...__VLS_functionalComponentArgsRest(__VLS_13));
    __VLS_15.slots.default;
    {
        const { description: __VLS_thisSlot } = __VLS_15.slots;
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
        (__VLS_ctx.error);
    }
    const __VLS_16 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
    }));
    const __VLS_18 = __VLS_17({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_17));
    let __VLS_20;
    let __VLS_21;
    let __VLS_22;
    const __VLS_23 = {
        onClick: (__VLS_ctx.fetchActivityDetail)
    };
    __VLS_19.slots.default;
    var __VLS_19;
    var __VLS_15;
}
else if (__VLS_ctx.registrationSuccess) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "success-page" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "success-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "success-header" },
    });
    const __VLS_24 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
        name: "checked",
        ...{ class: "success-icon" },
    }));
    const __VLS_26 = __VLS_25({
        name: "checked",
        ...{ class: "success-icon" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_25));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "registration-info-card" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "value" },
    });
    (__VLS_ctx.registrationInfo.regno);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "value" },
    });
    (__VLS_ctx.registrationInfo.name);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "value" },
    });
    (__VLS_ctx.registrationInfo.phone);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "value" },
    });
    (__VLS_ctx.registrationInfo.age);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "value" },
    });
    (__VLS_ctx.formatDateTime(__VLS_ctx.registrationInfo.create_time));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "qrcode-card" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "qrcode-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
        src: (__VLS_ctx.registrationInfo.qrcode),
        alt: "活动二维码",
        ...{ class: "qrcode-image" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "tips-card" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.ul, __VLS_intrinsicElements.ul)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.li, __VLS_intrinsicElements.li)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.li, __VLS_intrinsicElements.li)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.li, __VLS_intrinsicElements.li)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.li, __VLS_intrinsicElements.li)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "success-actions" },
    });
    const __VLS_28 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
        ...{ 'onClick': {} },
        round: true,
        block: true,
        type: "primary",
    }));
    const __VLS_30 = __VLS_29({
        ...{ 'onClick': {} },
        round: true,
        block: true,
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_29));
    let __VLS_32;
    let __VLS_33;
    let __VLS_34;
    const __VLS_35 = {
        onClick: (__VLS_ctx.goBack)
    };
    __VLS_31.slots.default;
    var __VLS_31;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-info-card" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-image-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
        src: (__VLS_ctx.activity.thumbnail),
        alt: (__VLS_ctx.activity.name),
        ...{ class: "activity-image" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-info" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({
        ...{ class: "activity-title" },
    });
    (__VLS_ctx.activity.name);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "activity-meta" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "meta-item" },
    });
    const __VLS_36 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
        name: "clock-o",
    }));
    const __VLS_38 = __VLS_37({
        name: "clock-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_37));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
    (__VLS_ctx.formatActivityTimeRange(__VLS_ctx.activity.startdate, __VLS_ctx.activity.enddate));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "meta-item" },
    });
    const __VLS_40 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
        name: "location-o",
    }));
    const __VLS_42 = __VLS_41({
        name: "location-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_41));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
    (__VLS_ctx.activity.location || '待定');
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "registration-form-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "form-header" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
    const __VLS_44 = {}.VanForm;
    /** @type {[typeof __VLS_components.VanForm, typeof __VLS_components.vanForm, typeof __VLS_components.VanForm, typeof __VLS_components.vanForm, ]} */ ;
    // @ts-ignore
    const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
        ...{ 'onSubmit': {} },
        ref: "formRef",
    }));
    const __VLS_46 = __VLS_45({
        ...{ 'onSubmit': {} },
        ref: "formRef",
    }, ...__VLS_functionalComponentArgsRest(__VLS_45));
    let __VLS_48;
    let __VLS_49;
    let __VLS_50;
    const __VLS_51 = {
        onSubmit: (__VLS_ctx.onSubmit)
    };
    /** @type {typeof __VLS_ctx.formRef} */ ;
    var __VLS_52 = {};
    __VLS_47.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "form-section" },
    });
    const __VLS_54 = {}.VanField;
    /** @type {[typeof __VLS_components.VanField, typeof __VLS_components.vanField, ]} */ ;
    // @ts-ignore
    const __VLS_55 = __VLS_asFunctionalComponent(__VLS_54, new __VLS_54({
        modelValue: (__VLS_ctx.formData.name),
        name: "name",
        label: "姓名",
        labelWidth: "70px",
        placeholder: "请输入您的真实姓名",
        rules: ([{ required: true, message: '请填写姓名' }]),
        leftIcon: "user-o",
        clearable: true,
    }));
    const __VLS_56 = __VLS_55({
        modelValue: (__VLS_ctx.formData.name),
        name: "name",
        label: "姓名",
        labelWidth: "70px",
        placeholder: "请输入您的真实姓名",
        rules: ([{ required: true, message: '请填写姓名' }]),
        leftIcon: "user-o",
        clearable: true,
    }, ...__VLS_functionalComponentArgsRest(__VLS_55));
    const __VLS_58 = {}.VanField;
    /** @type {[typeof __VLS_components.VanField, typeof __VLS_components.vanField, ]} */ ;
    // @ts-ignore
    const __VLS_59 = __VLS_asFunctionalComponent(__VLS_58, new __VLS_58({
        modelValue: (__VLS_ctx.formData.phone),
        name: "mobile",
        label: "手机号",
        labelWidth: "70px",
        placeholder: "请输入手机号码",
        rules: ([
            { required: true, message: '请填写手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
        ]),
        leftIcon: "phone-o",
        clearable: true,
    }));
    const __VLS_60 = __VLS_59({
        modelValue: (__VLS_ctx.formData.phone),
        name: "mobile",
        label: "手机号",
        labelWidth: "70px",
        placeholder: "请输入手机号码",
        rules: ([
            { required: true, message: '请填写手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
        ]),
        leftIcon: "phone-o",
        clearable: true,
    }, ...__VLS_functionalComponentArgsRest(__VLS_59));
    const __VLS_62 = {}.VanField;
    /** @type {[typeof __VLS_components.VanField, typeof __VLS_components.vanField, typeof __VLS_components.VanField, typeof __VLS_components.vanField, ]} */ ;
    // @ts-ignore
    const __VLS_63 = __VLS_asFunctionalComponent(__VLS_62, new __VLS_62({
        modelValue: (__VLS_ctx.formData.verificationCode),
        name: "verificationCode",
        label: "验证码",
        labelWidth: "70px",
        placeholder: "请输入短信验证码",
        rules: ([{ required: true, message: '请输入验证码' }]),
        leftIcon: "shield-o",
        clearable: true,
    }));
    const __VLS_64 = __VLS_63({
        modelValue: (__VLS_ctx.formData.verificationCode),
        name: "verificationCode",
        label: "验证码",
        labelWidth: "70px",
        placeholder: "请输入短信验证码",
        rules: ([{ required: true, message: '请输入验证码' }]),
        leftIcon: "shield-o",
        clearable: true,
    }, ...__VLS_functionalComponentArgsRest(__VLS_63));
    __VLS_65.slots.default;
    {
        const { button: __VLS_thisSlot } = __VLS_65.slots;
        const __VLS_66 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_67 = __VLS_asFunctionalComponent(__VLS_66, new __VLS_66({
            ...{ 'onClick': {} },
            size: "small",
            type: "primary",
            disabled: (!__VLS_ctx.canSendSms || __VLS_ctx.smsCountdown > 0),
            loading: (__VLS_ctx.sendingSms),
        }));
        const __VLS_68 = __VLS_67({
            ...{ 'onClick': {} },
            size: "small",
            type: "primary",
            disabled: (!__VLS_ctx.canSendSms || __VLS_ctx.smsCountdown > 0),
            loading: (__VLS_ctx.sendingSms),
        }, ...__VLS_functionalComponentArgsRest(__VLS_67));
        let __VLS_70;
        let __VLS_71;
        let __VLS_72;
        const __VLS_73 = {
            onClick: (__VLS_ctx.sendSmsCode)
        };
        __VLS_69.slots.default;
        (__VLS_ctx.getSmsButtonText());
        var __VLS_69;
    }
    var __VLS_65;
    const __VLS_74 = {}.VanField;
    /** @type {[typeof __VLS_components.VanField, typeof __VLS_components.vanField, ]} */ ;
    // @ts-ignore
    const __VLS_75 = __VLS_asFunctionalComponent(__VLS_74, new __VLS_74({
        modelValue: (__VLS_ctx.formData.age),
        name: "age",
        label: "年龄",
        labelWidth: "70px",
        placeholder: "请输入年龄",
        type: "number",
        rules: ([
            { required: true, message: '请填写年龄' },
            { pattern: /^(?:[1-9][0-9]?|1[01][0-9]|120)$/, message: '请输入正确的年龄(1-120)' }
        ]),
        leftIcon: "friends-o",
        clearable: true,
    }));
    const __VLS_76 = __VLS_75({
        modelValue: (__VLS_ctx.formData.age),
        name: "age",
        label: "年龄",
        labelWidth: "70px",
        placeholder: "请输入年龄",
        type: "number",
        rules: ([
            { required: true, message: '请填写年龄' },
            { pattern: /^(?:[1-9][0-9]?|1[01][0-9]|120)$/, message: '请输入正确的年龄(1-120)' }
        ]),
        leftIcon: "friends-o",
        clearable: true,
    }, ...__VLS_functionalComponentArgsRest(__VLS_75));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "agreement-section" },
    });
    const __VLS_78 = {}.VanCheckbox;
    /** @type {[typeof __VLS_components.VanCheckbox, typeof __VLS_components.vanCheckbox, typeof __VLS_components.VanCheckbox, typeof __VLS_components.vanCheckbox, ]} */ ;
    // @ts-ignore
    const __VLS_79 = __VLS_asFunctionalComponent(__VLS_78, new __VLS_78({
        modelValue: (__VLS_ctx.formData.agreeTerms),
    }));
    const __VLS_80 = __VLS_79({
        modelValue: (__VLS_ctx.formData.agreeTerms),
    }, ...__VLS_functionalComponentArgsRest(__VLS_79));
    __VLS_81.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ onClick: (__VLS_ctx.showAgreement) },
        ...{ class: "agreement-link" },
    });
    var __VLS_81;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "submit-section" },
    });
    const __VLS_82 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_83 = __VLS_asFunctionalComponent(__VLS_82, new __VLS_82({
        round: true,
        block: true,
        type: "primary",
        nativeType: "submit",
        loading: (__VLS_ctx.submitting),
        disabled: (!__VLS_ctx.formData.agreeTerms),
        loadingText: "提交中...",
        ...{ class: "submit-btn" },
    }));
    const __VLS_84 = __VLS_83({
        round: true,
        block: true,
        type: "primary",
        nativeType: "submit",
        loading: (__VLS_ctx.submitting),
        disabled: (!__VLS_ctx.formData.agreeTerms),
        loadingText: "提交中...",
        ...{ class: "submit-btn" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_83));
    __VLS_85.slots.default;
    var __VLS_85;
    var __VLS_47;
}
const __VLS_86 = {}.VanPopup;
/** @type {[typeof __VLS_components.VanPopup, typeof __VLS_components.vanPopup, typeof __VLS_components.VanPopup, typeof __VLS_components.vanPopup, ]} */ ;
// @ts-ignore
const __VLS_87 = __VLS_asFunctionalComponent(__VLS_86, new __VLS_86({
    show: (__VLS_ctx.showAgreementPopup),
    position: "bottom",
    ...{ style: ({ height: '70%' }) },
}));
const __VLS_88 = __VLS_87({
    show: (__VLS_ctx.showAgreementPopup),
    position: "bottom",
    ...{ style: ({ height: '70%' }) },
}, ...__VLS_functionalComponentArgsRest(__VLS_87));
__VLS_89.slots.default;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "agreement-popup" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "popup-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
const __VLS_90 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_91 = __VLS_asFunctionalComponent(__VLS_90, new __VLS_90({
    ...{ 'onClick': {} },
    name: "cross",
}));
const __VLS_92 = __VLS_91({
    ...{ 'onClick': {} },
    name: "cross",
}, ...__VLS_functionalComponentArgsRest(__VLS_91));
let __VLS_94;
let __VLS_95;
let __VLS_96;
const __VLS_97 = {
    onClick: (...[$event]) => {
        __VLS_ctx.showAgreementPopup = false;
    }
};
var __VLS_93;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "popup-content" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "agreement-content" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h4, __VLS_intrinsicElements.h4)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h4, __VLS_intrinsicElements.h4)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h4, __VLS_intrinsicElements.h4)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h4, __VLS_intrinsicElements.h4)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "popup-footer" },
});
const __VLS_98 = {}.VanButton;
/** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
// @ts-ignore
const __VLS_99 = __VLS_asFunctionalComponent(__VLS_98, new __VLS_98({
    ...{ 'onClick': {} },
    round: true,
    block: true,
    type: "primary",
}));
const __VLS_100 = __VLS_99({
    ...{ 'onClick': {} },
    round: true,
    block: true,
    type: "primary",
}, ...__VLS_functionalComponentArgsRest(__VLS_99));
let __VLS_102;
let __VLS_103;
let __VLS_104;
const __VLS_105 = {
    onClick: (__VLS_ctx.agreeAndClose)
};
__VLS_101.slots.default;
var __VLS_101;
var __VLS_89;
/** @type {__VLS_StyleScopedClasses['activity-registration-page']} */ ;
/** @type {__VLS_StyleScopedClasses['custom-header']} */ ;
/** @type {__VLS_StyleScopedClasses['header-content']} */ ;
/** @type {__VLS_StyleScopedClasses['back-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['header-title']} */ ;
/** @type {__VLS_StyleScopedClasses['header-placeholder']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['error-container']} */ ;
/** @type {__VLS_StyleScopedClasses['success-page']} */ ;
/** @type {__VLS_StyleScopedClasses['success-container']} */ ;
/** @type {__VLS_StyleScopedClasses['success-header']} */ ;
/** @type {__VLS_StyleScopedClasses['success-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['registration-info-card']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['label']} */ ;
/** @type {__VLS_StyleScopedClasses['value']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['label']} */ ;
/** @type {__VLS_StyleScopedClasses['value']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['label']} */ ;
/** @type {__VLS_StyleScopedClasses['value']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['label']} */ ;
/** @type {__VLS_StyleScopedClasses['value']} */ ;
/** @type {__VLS_StyleScopedClasses['info-item']} */ ;
/** @type {__VLS_StyleScopedClasses['label']} */ ;
/** @type {__VLS_StyleScopedClasses['value']} */ ;
/** @type {__VLS_StyleScopedClasses['qrcode-card']} */ ;
/** @type {__VLS_StyleScopedClasses['qrcode-container']} */ ;
/** @type {__VLS_StyleScopedClasses['qrcode-image']} */ ;
/** @type {__VLS_StyleScopedClasses['tips-card']} */ ;
/** @type {__VLS_StyleScopedClasses['success-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-info']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['registration-form-container']} */ ;
/** @type {__VLS_StyleScopedClasses['form-header']} */ ;
/** @type {__VLS_StyleScopedClasses['form-section']} */ ;
/** @type {__VLS_StyleScopedClasses['agreement-section']} */ ;
/** @type {__VLS_StyleScopedClasses['agreement-link']} */ ;
/** @type {__VLS_StyleScopedClasses['submit-section']} */ ;
/** @type {__VLS_StyleScopedClasses['submit-btn']} */ ;
/** @type {__VLS_StyleScopedClasses['agreement-popup']} */ ;
/** @type {__VLS_StyleScopedClasses['popup-header']} */ ;
/** @type {__VLS_StyleScopedClasses['popup-content']} */ ;
/** @type {__VLS_StyleScopedClasses['agreement-content']} */ ;
/** @type {__VLS_StyleScopedClasses['popup-footer']} */ ;
// @ts-ignore
var __VLS_53 = __VLS_52;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            formatActivityTimeRange: formatActivityTimeRange,
            formatDateTime: formatDateTime,
            loading: loading,
            error: error,
            submitting: submitting,
            showAgreementPopup: showAgreementPopup,
            formRef: formRef,
            sendingSms: sendingSms,
            smsCountdown: smsCountdown,
            registrationSuccess: registrationSuccess,
            registrationInfo: registrationInfo,
            formData: formData,
            goBack: goBack,
            activity: activity,
            fetchActivityDetail: fetchActivityDetail,
            canSendSms: canSendSms,
            getSmsButtonText: getSmsButtonText,
            sendSmsCode: sendSmsCode,
            showAgreement: showAgreement,
            agreeAndClose: agreeAndClose,
            onSubmit: onSubmit,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
