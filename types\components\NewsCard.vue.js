import { defineProps, defineEmits } from 'vue';
const props = withDefaults(defineProps(), {
    showMeta: false,
    clickable: true
});
const emit = defineEmits();
// 处理点击事件
const handleClick = () => {
    if (props.clickable) {
        emit('click', props.newsItem);
    }
};
// 处理图片加载错误
const handleImageError = () => {
    emit('imageError', props.newsItem);
};
// 格式化日期
const formatDate = (dateString) => {
    try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}.${month}.${day}`;
    }
    catch {
        return dateString;
    }
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    showMeta: false,
    clickable: true
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-content']} */ ;
/** @type {__VLS_StyleScopedClasses['news-title']} */ ;
/** @type {__VLS_StyleScopedClasses['news-description']} */ ;
/** @type {__VLS_StyleScopedClasses['news-category']} */ ;
/** @type {__VLS_StyleScopedClasses['news-date']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-content']} */ ;
/** @type {__VLS_StyleScopedClasses['news-title']} */ ;
/** @type {__VLS_StyleScopedClasses['news-description']} */ ;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ onClick: (__VLS_ctx.handleClick) },
    ...{ class: "news-card" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-image-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
    ...{ onError: (__VLS_ctx.handleImageError) },
    src: (__VLS_ctx.newsItem.thumbnail),
    alt: (__VLS_ctx.newsItem.name),
    ...{ class: "news-image" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "image-overlay" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-content" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-header" },
});
if (__VLS_ctx.newsItem.category) {
    const __VLS_0 = {}.VanTag;
    /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        type: "primary",
        size: "medium",
        ...{ class: "news-category" },
    }));
    const __VLS_2 = __VLS_1({
        type: "primary",
        size: "medium",
        ...{ class: "news-category" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_3.slots.default;
    (__VLS_ctx.newsItem.category);
    var __VLS_3;
}
if (__VLS_ctx.newsItem.publishDate) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "news-date" },
    });
    (__VLS_ctx.formatDate(__VLS_ctx.newsItem.publishDate));
}
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
    ...{ class: "news-title" },
});
(__VLS_ctx.newsItem.name);
if (__VLS_ctx.newsItem.desc) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
        ...{ class: "news-description" },
    });
    (__VLS_ctx.newsItem.desc);
}
if (__VLS_ctx.showMeta) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "news-meta" },
    });
    if (__VLS_ctx.newsItem.author) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "meta-item" },
        });
        const __VLS_4 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
            name: "user-o",
            size: "12",
        }));
        const __VLS_6 = __VLS_5({
            name: "user-o",
            size: "12",
        }, ...__VLS_functionalComponentArgsRest(__VLS_5));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        (__VLS_ctx.newsItem.author);
    }
    if (__VLS_ctx.newsItem.views) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "meta-item" },
        });
        const __VLS_8 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
            name: "eye-o",
            size: "12",
        }));
        const __VLS_10 = __VLS_9({
            name: "eye-o",
            size: "12",
        }, ...__VLS_functionalComponentArgsRest(__VLS_9));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        (__VLS_ctx.newsItem.views);
    }
}
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image']} */ ;
/** @type {__VLS_StyleScopedClasses['image-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['news-content']} */ ;
/** @type {__VLS_StyleScopedClasses['news-header']} */ ;
/** @type {__VLS_StyleScopedClasses['news-category']} */ ;
/** @type {__VLS_StyleScopedClasses['news-date']} */ ;
/** @type {__VLS_StyleScopedClasses['news-title']} */ ;
/** @type {__VLS_StyleScopedClasses['news-description']} */ ;
/** @type {__VLS_StyleScopedClasses['news-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            handleClick: handleClick,
            handleImageError: handleImageError,
            formatDate: formatDate,
        };
    },
    __typeEmits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    __typeEmits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
