import { get, post, requestWithRetry } from '../../api/request';
import { ACTIVITY_URLS, buildUrl } from '../../api/urls';

/**
 * 获取活动列表
 * @param params 查询参数 - 支持 param: 'current' | 'upcoming' | 'past'
 * @returns 活动列表数据
 */
export function getActivityList(params?: any) {
  return get<ActivityListResponse>(ACTIVITY_URLS.ACTIVITY, params);
}

/**
 * 获取活动列表（带重试）
 * @param params 查询参数 - 支持 param: 'current' | 'upcoming' | 'past'
 * @returns 活动列表数据
 */
export function getActivityListlWithRetry(params?: any) {
  return requestWithRetry<ActivityListResponse>(() => getActivityList(params));
}

/**
 * 获取本月活动列表
 * @param params 其他查询参数
 * @returns 活动列表数据
 */
export function getCurrentMonthActivities(params?: any) {
  return getActivityListlWithRetry({ ...params, param: 'current' });
}

/**
 * 获取历史活动列表
 * @param params 其他查询参数
 * @returns 活动列表数据
 */
export function getPastActivities(params?: any) {
  return getActivityListlWithRetry({ ...params, param: 'past' });
}

/**
 * 获取活动预告列表
 * @param params 其他查询参数
 * @returns 活动列表数据
 */
export function getUpcomingActivities(params?: any) {
  return getActivityListlWithRetry({ ...params, param: 'upcoming' });
}

export function getActivityDetail(id: string | number) {
  return get<ActivityDetaiResponse>(buildUrl(ACTIVITY_URLS.ACTIVITY, id));
}
export function getHomeActivityList(params?: any) {
  return get<HomeActivityListResponse>(ACTIVITY_URLS.HOME_CONTENT, params);
}

// 活动数据类型定义
export interface ActivityItem {
  id: number;
  name: string;
  cat: string;
  cat_name: string; //为了兼容组件，这里实际是cat_name
  desc: string;
  thumbnail: string;
  isfree: number;
  startdate: string; // datetime格式：YYYY-MM-DD HH:mm:ss
  enddate: string;   // datetime格式：YYYY-MM-DD HH:mm:ss
  upload_time?: string; // datetime格式：YYYY-MM-DD HH:mm:ss 上传时间
  views: number;
  location: string;
  tags?: string[];
}
export interface ActivityDetailItem {
  id: number;
  name: string;
  cat: string;
  cat_name: string;
  desc: string;
  thumbnail: string;
  isfree: number;
  startdate: string; // datetime格式：YYYY-MM-DD HH:mm:ss
  enddate: string;   // datetime格式：YYYY-MM-DD HH:mm:ss
  upload_time?: string; // datetime格式：YYYY-MM-DD HH:mm:ss 上传时间
  views: number;
  location: string;
  content: string; // HTML格式的详细内容
}
export interface ActivityDetaiResponse {
  activity: {
    id: number;
    name: string;
    cat: string;
    cat_name: string;
    desc: string;
    thumbnail: string;
    isfree: number;
    startdate: string; // datetime格式：YYYY-MM-DD HH:mm:ss
    enddate: string;   // datetime格式：YYYY-MM-DD HH:mm:ss
    upload_time?: string; // datetime格式：YYYY-MM-DD HH:mm:ss 上传时间
    views: number;
    location: string;
    content: string;
  }
}

export interface ActivityListResponse {
  results: ActivityItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}

export interface HomeActivityListResponse{
  items: {
    activity_data: ActivityItem[];
  }
}