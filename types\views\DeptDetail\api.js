import { get, requestWithRetry } from '../../api/request';
import { DEPT_URLS, buildUrl } from '../../api/urls';
/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getDeptDetail(id) {
    return get(buildUrl(DEPT_URLS.DEPT, id));
}
/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getDeptDetailWithRetry(id) {
    return requestWithRetry(() => getDeptDetail(id));
}
