/**
 * 简化的分享工具函数
 * 提供基本的文本分享功能，适用于微信等社交平台
 */
import { formatActivityTimeRange } from './dateTime';
/**
 * 生成活动分享文本
 * @param activity 活动数据
 * @returns 分享文本
 */
export function generateShareText(activity) {
    console.log('=== 生成分享文本 ===');
    console.log('输入的活动数据:', activity);
    // 严格的数据验证
    if (!activity) {
        console.error('活动数据为空');
        throw new Error('活动数据不能为空');
    }
    if (!activity.id) {
        console.error('活动ID缺失');
        throw new Error('活动ID不能为空');
    }
    const baseUrl = window.location.origin;
    const activityUrl = `${baseUrl}/#/activity-detail/${activity.id}`;
    // 数据验证和默认值处理
    const activityName = activity.name && activity.name.trim() ? activity.name.trim() : '精彩活动';
    const activityDesc = activity.desc && activity.desc.trim() ? activity.desc.trim() : '';
    console.log('处理后的活动名称:', activityName);
    console.log('处理后的活动描述:', activityDesc);
    // 使用统一的时间格式化工具
    const timeText = formatActivityTimeRange(activity.startdate || '', activity.enddate || '');
    const location = activity.location && activity.location.trim() ? activity.location.trim() : '医院';
    const fee = activity.isfree === '免费' || activity.price === 0
        ? '免费' : (activity.price ? `¥${activity.price}` : '费用待定');
    const participants = activity.current_participants || 0;
    const maxParticipants = activity.max_participants;
    const participantText = maxParticipants
        ? `${participants}/${maxParticipants}人`
        : `${participants}人`;
    // 生成分享文本
    const shareText = `📢 ${activityName}

⏰ 时间：${timeText}
📍 地点：${location}
💰 费用：${fee}

${activityDesc ? activityDesc + '\n\n' : ''}点击链接查看详情：
${activityUrl}`;
    console.log('生成的分享文本:', shareText);
    console.log('=== 分享文本生成完成 ===');
    return shareText;
}
/**
 * 检测是否在微信环境中
 * @returns 是否在微信中
 */
export function isWeChatEnvironment() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('micromessenger');
}
/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 复制是否成功
 */
export async function copyToClipboard(text) {
    console.log('=== 复制到剪贴板 ===');
    console.log('要复制的文本长度:', text.length);
    console.log('文本内容预览:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
    if (!text || text.trim() === '') {
        console.error('复制失败：文本为空');
        return false;
    }
    try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            console.log('使用现代剪贴板API');
            await navigator.clipboard.writeText(text);
            console.log('现代剪贴板API复制成功');
            return true;
        }
        else {
            console.log('使用传统复制方法');
            // 降级方案：使用传统的复制方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            console.log('传统复制方法结果:', result);
            return result;
        }
    }
    catch (err) {
        console.error('复制失败:', err);
        return false;
    }
}
/**
 * 检测是否支持原生分享
 * @returns 是否支持原生分享
 */
export function canUseNativeShare() {
    return 'share' in navigator && navigator.share !== undefined;
}
/**
 * 执行分享操作
 * @param activity 活动数据
 * @returns Promise<{ success: boolean, method: string }> 分享结果和使用的方法
 */
export async function shareActivity(activity) {
    console.log('=== 执行分享操作 ===');
    console.log('分享活动数据:', activity);
    try {
        // 数据验证
        if (!activity) {
            console.error('分享失败：活动数据为空');
            return { success: false, method: 'validation_failed' };
        }
        if (!activity.id) {
            console.error('分享失败：活动ID为空');
            return { success: false, method: 'validation_failed' };
        }
        if (!activity.name || activity.name.trim() === '') {
            console.error('分享失败：活动名称为空');
            return { success: false, method: 'validation_failed' };
        }
        const shareText = generateShareText(activity);
        const baseUrl = window.location.origin;
        const activityUrl = `${baseUrl}/#/activity-detail/${activity.id}`;
        console.log('分享文本生成成功，长度:', shareText.length);
        // 尝试使用原生分享API（移动端浏览器支持）
        if (canUseNativeShare()) {
            try {
                console.log('使用原生分享API');
                await navigator.share({
                    title: activity.name,
                    text: shareText,
                    url: activityUrl
                });
                console.log('原生分享成功');
                return { success: true, method: 'native' };
            }
            catch (err) {
                // 用户取消分享或分享失败
                if (err.name === 'AbortError') {
                    console.log('用户取消原生分享');
                    return { success: false, method: 'cancelled' };
                }
                console.log('原生分享失败，使用复制方式:', err);
            }
        }
        // 在微信环境中，尝试特殊处理
        if (isWeChatEnvironment()) {
            console.log('检测到微信环境');
            // 微信内置浏览器通常不支持navigator.share
            // 但可以尝试其他方式
            try {
                // 尝试调用微信分享（如果页面集成了微信JS-SDK）
                if (typeof window.wx !== 'undefined') {
                    const wx = window.wx;
                    // 这里需要微信JS-SDK的配置，通常需要后端配合
                    console.log('检测到微信JS-SDK，但需要后端配置签名');
                }
            }
            catch (err) {
                console.log('微信分享尝试失败:', err);
            }
        }
        // 降级到复制文本
        console.log('使用复制到剪贴板方式');
        const copySuccess = await copyToClipboard(shareText);
        console.log('复制结果:', copySuccess);
        return {
            success: copySuccess,
            method: copySuccess ? 'clipboard' : 'failed'
        };
    }
    catch (error) {
        console.error('分享过程中发生异常:', error);
        return { success: false, method: 'error' };
    }
}
/**
 * 尝试调起系统分享菜单（适用于移动端）
 * @param activity 活动数据
 * @returns Promise<boolean> 分享是否成功
 */
export async function shareWithSystemMenu(activity) {
    const shareText = generateShareText(activity);
    const baseUrl = window.location.origin;
    const activityUrl = `${baseUrl}/#/activity-detail/${activity.id}`;
    // 检查是否支持Web Share API
    if ('share' in navigator) {
        try {
            await navigator.share({
                title: activity.name,
                text: shareText,
                url: activityUrl
            });
            return true;
        }
        catch (err) {
            if (err.name === 'AbortError') {
                // 用户取消了分享
                console.log('用户取消分享');
                return false;
            }
            else {
                console.error('分享失败:', err);
                throw err;
            }
        }
    }
    else {
        throw new Error('浏览器不支持原生分享');
    }
}
