<template>
  <div class="my-page">
    <!-- 使用通用顶部导航栏 -->
    <GlobalHeader title="我的" />
    
    <!-- 个人信息卡片 -->
    <div class="profile-card">
      <div class="profile-header">
        <div class="avatar-container">
          <img 
            :src="userInfo.avatar || defaultAvatar" 
            alt="头像" 
            class="avatar" 
            @click="changeAvatar"
            @error="handleImageError" 
          />
          <div class="avatar-edit-icon">
            <van-icon name="edit" />
          </div>
        </div>
        <div class="profile-info">
          <h3 class="username">{{ userInfo.name || '未设置' }}</h3>
          <p class="phone">{{ userInfo.phone || '未绑定手机号' }}</p>
          <p class="account">账号：{{ userInfo.account || '未设置' }}</p>
        </div>
      </div>
    </div>

    <!-- 积分信息 -->
    <div class="points-card">
      <div class="points-header">
        <h3>我的积分</h3>
        <span class="points-value">{{ userInfo.points || 0 }}</span>
      </div>
      <div class="points-actions">
        <van-button size="small" type="primary" @click="viewPointsHistory">积分明细</van-button>
        <van-button size="small" type="default" @click="pointsExchange">积分兑换</van-button>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell title="参与的活动" :value="userActivities.length + '个'" is-link @click="viewMyActivities">
          <template #icon>
            <van-icon name="calendar-o" color="#4b8bf4" />
          </template>
        </van-cell>
        <van-cell title="获奖情况" :value="userAwards.length + '个'" is-link @click="viewMyAwards">
          <template #icon>
            <van-icon name="award-o" color="#ff9500" />
          </template>
        </van-cell>
        <van-cell title="个人设置" is-link @click="openSettings">
          <template #icon>
            <van-icon name="setting-o" color="#666" />
          </template>
        </van-cell>
        <van-cell title="帮助与反馈" is-link @click="openHelp">
          <template #icon>
            <van-icon name="question-o" color="#666" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 参与的活动列表 -->
    <div v-if="showActivities" class="activities-section">
      <div class="section-header">
        <h3>我参与的活动</h3>
        <van-button size="small" type="primary" @click="showActivities = false">收起</van-button>
      </div>
      <div class="activities-list">
        <div v-for="activity in userActivities" :key="activity.id" class="activity-item">
          <img 
            :src="activity.thumbnail" 
            alt="活动图片" 
            class="activity-thumb"
            @error="handleActivityImageError"
          />
          <div class="activity-info">
            <h4>{{ activity.name }}</h4>
            <p class="activity-time">{{ formatDateTime(activity.startdate) }}</p>
            <p class="activity-status" :class="getStatusClass(activity.status)">{{ activity.status }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 获奖情况列表 -->
    <div v-if="showAwards" class="awards-section">
      <div class="section-header">
        <h3>我的获奖情况</h3>
        <van-button size="small" type="primary" @click="showAwards = false">收起</van-button>
      </div>
      <div class="awards-list">
        <div v-for="award in userAwards" :key="award.id" class="award-item">
          <div class="award-icon">
            <van-icon name="award-o" color="#ff9500" size="24" />
          </div>
          <div class="award-info">
            <h4>{{ award.title }}</h4>
            <p class="award-desc">{{ award.description }}</p>
            <p class="award-time">{{ formatDateTime(award.awardTime) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showImagePreview } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import { formatDateTime } from '../../utils/dateTime';
import 'vant/es/toast/style';
import 'vant/es/image-preview/style';

const router = useRouter();

// 用户信息
const userInfo = ref({
  name: '',
  phone: '',
  account: '',
  avatar: '',
  points: 0
});

// 默认头像 - 使用现有的SVG文件
const defaultAvatar = '/images/default-avatar.svg';

// 用户活动 - 使用icon目录中的图片
const userActivities = ref([
  {
    id: 1,
    name: '中医养生讲座',
    thumbnail: '/icon/1.png', // 使用实际存在的图片
    startdate: '2024-01-15 14:00:00',
    status: '已参加'
  },
  {
    id: 2,
    name: '针灸体验活动',
    thumbnail: '/icon/2.png', // 使用实际存在的图片
    startdate: '2024-02-20 10:00:00',
    status: '已报名'
  },
  {
    id: 3,
    name: '中药材识别课程',
    thumbnail: '/icon/3.png', // 添加第三个活动
    startdate: '2024-03-10 09:00:00',
    status: '已完成'
  }
]);

// 用户获奖 - 可以添加更多获奖记录
const userAwards = ref([
  {
    id: 1,
    title: '优秀参与者',
    description: '中医养生讲座优秀参与者',
    awardTime: '2024-01-16 16:00:00'
  },
  {
    id: 2,
    title: '学习达人',
    description: '连续参与3次中医课程学习',
    awardTime: '2024-02-28 18:00:00'
  }
]);

// 显示状态
const showActivities = ref(true);  // 改为 true，默认展开
const showAwards = ref(true);      // 改为 true，默认展开

// 更换头像
const changeAvatar = () => {
  showToast('头像更换功能开发中');
};

// 查看积分明细
const viewPointsHistory = () => {
  showToast('积分明细功能开发中');
};

// 积分兑换
const pointsExchange = () => {
  showToast('积分兑换功能开发中');
};

// 查看我的活动
const viewMyActivities = () => {
  showActivities.value = !showActivities.value;
};

// 查看我的获奖
const viewMyAwards = () => {
  showAwards.value = !showAwards.value;
};

// 个人设置
const openSettings = () => {
  showToast('个人设置功能开发中');
};

// 帮助与反馈
const openHelp = () => {
  showToast('帮助与反馈功能开发中');
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case '已参加':
      return 'status-completed';
    case '已报名':
      return 'status-registered';
    case '已取消':
      return 'status-cancelled';
    default:
      return '';
  }
};

// 加载用户信息
const loadUserInfo = () => {
  // 从本地存储或API获取用户信息
  const savedUserInfo = localStorage.getItem('userInfo');
  if (savedUserInfo) {
    userInfo.value = JSON.parse(savedUserInfo);
  }
};

onMounted(() => {
  loadUserInfo();
});

// 处理活动图片加载错误
const handleActivityImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = '/images/default-news.svg'; // 使用默认新闻图片作为备用
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = defaultAvatar;
};
</script>

<style scoped>
.my-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px;
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
  padding: 0 10px; /* 添加统一的左右内边距 */
}

.profile-card {
  margin: 16px 0; /* 只保留上下外边距 */
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.points-card {
  margin: 16px 0; /* 只保留上下外边距 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.menu-section {
  margin: 16px 0; /* 只保留上下外边距 */
}

.activities-section, .awards-section {
  margin: 16px 0; /* 只保留上下外边距 */
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 响应式设计调整 */
@media (max-width: 768px) {
  .my-page {
    padding: 0 8px; /* 移动端减少左右内边距 */
  }
  
  .profile-card, .points-card, .menu-section, .activities-section, .awards-section {
    margin: 12px 0; /* 移动端减少上下外边距 */
  }
  
  .username {
    font-size: 18px;
  }
  
  .points-value {
    font-size: 24px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-card, .points-card, .menu-section {
  animation: fadeInUp 0.6s ease-out;
}

.activities-section {
  animation: fadeInUp 0.8s ease-out;
}

.awards-section {
  animation: fadeInUp 1s ease-out;
}
</style>