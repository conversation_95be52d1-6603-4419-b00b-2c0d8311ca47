<template>
  <div class="my-page">
    <!-- 使用通用顶部导航栏 -->
    <GlobalHeader title="我的" />

    <!-- 个人信息区域 -->
    <div class="profile-section">
      <!-- 背景装饰 -->
      <div class="profile-bg">
        <img src="/images/profile-bg-pattern.svg" alt="背景装饰" class="bg-pattern" />
      </div>

      <!-- 个人信息卡片 -->
      <div class="profile-card">
        <div class="avatar-container" @click="changeAvatar">
          <div class="avatar-wrapper">
            <img
              :src="userInfo.avatar || defaultAvatar"
              alt="头像"
              class="avatar"
              @error="handleImageError"
            />
            <div class="avatar-edit-overlay">
              <van-icon name="edit" size="16" color="white" />
            </div>
          </div>
          <div class="avatar-ring"></div>
        </div>

        <div class="profile-info">
          <h2 class="username">{{ userInfo.name || '未设置姓名' }}</h2>
          <p class="user-title">{{ userInfo.title || '中医爱好者' }}</p>
          <div class="user-details">
            <div class="detail-item">
              <van-icon name="phone-o" size="14" />
              <span>{{ userInfo.phone || '未绑定手机号' }}</span>
            </div>
            <div class="detail-item">
              <van-icon name="user-o" size="14" />
              <span>{{ userInfo.account || '未设置账号' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计区域 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-item" @click="viewPointsHistory">
          <div class="stat-icon">
            <img src="/images/points-icon.svg" alt="积分" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ userInfo.points || 0 }}</div>
            <div class="stat-label">我的积分</div>
          </div>
        </div>

        <div class="stat-item" @click="viewMyActivities">
          <div class="stat-icon">
            <img src="/images/activity-icon.svg" alt="活动" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ userActivities.length }}</div>
            <div class="stat-label">参与活动</div>
          </div>
        </div>

        <div class="stat-item" @click="viewMyFavorites">
          <div class="stat-icon">
            <img src="/images/favorite-icon.svg" alt="收藏" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ userFavorites.length }}</div>
            <div class="stat-label">我的收藏</div>
          </div>
        </div>

        <div class="stat-item" @click="viewMyAwards">
          <div class="stat-icon">
            <img src="/images/trophy-icon.svg" alt="奖项" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ userAwards.length }}</div>
            <div class="stat-label">获得奖项</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能菜单区域 -->
    <div class="menu-section">
      <div class="menu-title">
        <h3>功能菜单</h3>
      </div>
      <div class="menu-grid">
        <div class="menu-item" @click="viewMyActivities">
          <div class="menu-icon">
            <van-icon name="calendar-o" size="24" color="#667eea" />
          </div>
          <span class="menu-text">我的活动</span>
          <van-icon name="arrow" size="16" color="#999" />
        </div>

        <div class="menu-item" @click="viewMyAwards">
          <div class="menu-icon">
            <van-icon name="award-o" size="24" color="#ff9500" />
          </div>
          <span class="menu-text">获奖情况</span>
          <van-icon name="arrow" size="16" color="#999" />
        </div>

        <div class="menu-item" @click="viewMyFavorites">
          <div class="menu-icon">
            <van-icon name="star-o" size="24" color="#ff6b6b" />
          </div>
          <span class="menu-text">我的收藏</span>
          <van-icon name="arrow" size="16" color="#999" />
        </div>

        <div class="menu-item" @click="openSettings">
          <div class="menu-icon">
            <img src="/images/settings-icon.svg" alt="设置" style="width: 24px; height: 24px;" />
          </div>
          <span class="menu-text">个人设置</span>
          <van-icon name="arrow" size="16" color="#999" />
        </div>

        <div class="menu-item" @click="openHelp">
          <div class="menu-icon">
            <img src="/images/help-icon.svg" alt="帮助" style="width: 24px; height: 24px;" />
          </div>
          <span class="menu-text">帮助反馈</span>
          <van-icon name="arrow" size="16" color="#999" />
        </div>

        <div class="menu-item" @click="pointsExchange">
          <div class="menu-icon">
            <van-icon name="gift-o" size="24" color="#26de81" />
          </div>
          <span class="menu-text">积分兑换</span>
          <van-icon name="arrow" size="16" color="#999" />
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <div class="action-title">
        <h3>快速操作</h3>
      </div>
      <div class="action-buttons">
        <button class="action-btn primary" @click="viewPointsHistory">
          <van-icon name="gold-coin-o" size="20" />
          <span>积分明细</span>
        </button>
        <button class="action-btn secondary" @click="pointsExchange">
          <van-icon name="gift-o" size="20" />
          <span>积分兑换</span>
        </button>
      </div>
    </div>

    <!-- 参与的活动列表 -->
    <div v-if="showActivities" class="content-section activities-section">
      <div class="section-header">
        <h3>
          <van-icon name="calendar-o" size="20" color="#667eea" />
          我参与的活动
        </h3>
        <van-button size="small" type="primary" @click="showActivities = false">收起</van-button>
      </div>
      <div class="content-list">
        <div v-for="activity in userActivities" :key="activity.id" class="list-item activity-item">
          <div class="item-image">
            <img
              :src="activity.thumbnail"
              alt="活动图片"
              class="thumbnail"
              @error="handleActivityImageError"
            />
            <div class="status-badge" :class="getStatusClass(activity.status)">
              {{ activity.status }}
            </div>
          </div>
          <div class="item-content">
            <h4 class="item-title">{{ activity.name }}</h4>
            <p class="item-time">
              <van-icon name="clock-o" size="14" />
              {{ formatDateTime(activity.startdate) }}
            </p>
            <p class="item-desc">{{ activity.description || '暂无描述' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 获奖情况列表 -->
    <div v-if="showAwards" class="content-section awards-section">
      <div class="section-header">
        <h3>
          <van-icon name="award-o" size="20" color="#ff9500" />
          我的获奖情况
        </h3>
        <van-button size="small" type="primary" @click="showAwards = false">收起</van-button>
      </div>
      <div class="content-list">
        <div v-for="award in userAwards" :key="award.id" class="list-item award-item">
          <div class="award-icon">
            <img src="/images/trophy-icon.svg" alt="奖杯" />
          </div>
          <div class="item-content">
            <h4 class="item-title">{{ award.title }}</h4>
            <p class="item-time">
              <van-icon name="clock-o" size="14" />
              {{ formatDateTime(award.awardDate) }}
            </p>
            <p class="item-desc">{{ award.description || '恭喜获得此奖项！' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 收藏列表 -->
    <div v-if="showFavorites" class="content-section favorites-section">
      <div class="section-header">
        <h3>
          <van-icon name="star-o" size="20" color="#ff6b6b" />
          我的收藏
        </h3>
        <van-button size="small" type="primary" @click="showFavorites = false">收起</van-button>
      </div>
      <div class="content-list">
        <div v-for="favorite in userFavorites" :key="favorite.id" class="list-item favorite-item">
          <div class="item-image">
            <img
              :src="favorite.thumbnail"
              alt="收藏图片"
              class="thumbnail"
              @error="handleActivityImageError"
            />
          </div>
          <div class="item-content">
            <h4 class="item-title">{{ favorite.title }}</h4>
            <p class="item-time">
              <van-icon name="clock-o" size="14" />
              收藏于 {{ formatDateTime(favorite.favoriteDate) }}
            </p>
            <p class="item-desc">{{ favorite.description || '暂无描述' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showImagePreview } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import { formatDateTime } from '../../utils/dateTime';
import 'vant/es/toast/style';
import 'vant/es/image-preview/style';

const router = useRouter();

// 用户信息
const userInfo = ref({
  name: '张医生',
  phone: '138****8888',
  account: 'zhangys2024',
  avatar: '',
  title: '中医养生专家',
  points: 1580
});

// 默认头像
const defaultAvatar = '/images/default-avatar.svg';

// 用户活动数据
const userActivities = ref([
  {
    id: 1,
    name: '中医养生讲座',
    startdate: '2024-01-15 14:00:00',
    status: '已参加',
    thumbnail: '/images/default-news.svg',
    description: '学习中医养生知识，提升健康意识'
  },
  {
    id: 2,
    name: '针灸体验活动',
    startdate: '2024-01-20 10:00:00',
    status: '已报名',
    thumbnail: '/images/default-news.svg',
    description: '体验传统针灸疗法，感受中医魅力'
  },
  {
    id: 3,
    name: '中药材识别大赛',
    startdate: '2024-01-25 09:00:00',
    status: '已取消',
    thumbnail: '/images/default-news.svg',
    description: '学习识别常见中药材，增长中医知识'
  }
]);

// 用户获奖数据
const userAwards = ref([
  {
    id: 1,
    title: '优秀学员',
    description: '在中医养生讲座中表现优秀',
    awardDate: '2024-01-16 16:00:00'
  },
  {
    id: 2,
    title: '积极参与奖',
    description: '积极参与各类中医文化活动',
    awardDate: '2024-01-22 15:30:00'
  }
]);

// 用户收藏数据
const userFavorites = ref([
  {
    id: 1,
    title: '中医四季养生指南',
    description: '详细介绍四季养生的中医理论和实践方法',
    thumbnail: '/images/default-news.svg',
    favoriteDate: '2024-01-10 09:30:00'
  },
  {
    id: 2,
    title: '常用中药材功效大全',
    description: '全面介绍常用中药材的功效和使用方法',
    thumbnail: '/images/default-news.svg',
    favoriteDate: '2024-01-12 14:20:00'
  },
  {
    id: 3,
    title: '针灸穴位图解',
    description: '图文并茂地介绍人体主要穴位和针灸方法',
    thumbnail: '/images/default-news.svg',
    favoriteDate: '2024-01-18 11:15:00'
  }
]);

// 显示状态
const showActivities = ref(false);
const showAwards = ref(false);
const showFavorites = ref(false);

// 更换头像
const changeAvatar = () => {
  showToast('头像更换功能开发中');
};

// 查看积分明细
const viewPointsHistory = () => {
  showToast('积分明细功能开发中');
};

// 积分兑换
const pointsExchange = () => {
  showToast('积分兑换功能开发中');
};

// 查看我的活动
const viewMyActivities = () => {
  showActivities.value = !showActivities.value;
};

// 查看我的获奖
const viewMyAwards = () => {
  showAwards.value = !showAwards.value;
};

// 查看我的收藏
const viewMyFavorites = () => {
  showFavorites.value = !showFavorites.value;
};

// 个人设置
const openSettings = () => {
  showToast('个人设置功能开发中');
};

// 帮助与反馈
const openHelp = () => {
  showToast('帮助与反馈功能开发中');
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case '已参加':
      return 'status-completed';
    case '已报名':
      return 'status-registered';
    case '已取消':
      return 'status-cancelled';
    default:
      return '';
  }
};

// 加载用户信息
const loadUserInfo = () => {
  // 从本地存储或API获取用户信息
  const savedUserInfo = localStorage.getItem('userInfo');
  if (savedUserInfo) {
    userInfo.value = JSON.parse(savedUserInfo);
  }
};

onMounted(() => {
  loadUserInfo();
});

// 处理活动图片加载错误
const handleActivityImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = '/images/default-news.svg'; // 使用默认新闻图片作为备用
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = defaultAvatar;
};
</script>

<style scoped>
.my-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px;
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 个人信息区域 */
.profile-section {
  position: relative;
  margin: 0 16px 20px 16px;
  overflow: hidden;
}

.profile-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  overflow: hidden;
  border-radius: 20px;
  z-index: 1;
}

.bg-pattern {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px 20px 20px;
  margin-top: 120px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 2;
}

.avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  cursor: pointer;
}

.avatar-wrapper {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.avatar-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.avatar-ring {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.profile-info {
  text-align: center;
}

.username {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  padding: 4px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  display: inline-block;
}

.user-details {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.detail-item .van-icon {
  color: #667eea;
}

/* 数据统计区域 */
.stats-section {
  margin: 0 16px 20px 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.stat-item {
  background: white;
  border-radius: 16px;
  padding: 16px 8px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-icon {
  width: 32px;
  height: 32px;
  margin: 0 auto 8px;
}

.stat-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 功能菜单区域 */
.menu-section {
  margin: 0 16px 20px 16px;
}

.menu-title {
  margin-bottom: 16px;
}

.menu-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.menu-grid {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background: rgba(102, 126, 234, 0.05);
}

.menu-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  margin-right: 16px;
}

.menu-text {
  flex: 1;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 快速操作区域 */
.quick-actions {
  margin: 0 16px 20px 16px;
}

.action-title {
  margin-bottom: 16px;
}

.action-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: none;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: white;
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* 内容区域 */
.content-section {
  margin: 0 16px 20px 16px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f5f5f5;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.content-list {
  padding: 0;
}

.list-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background: rgba(102, 126, 234, 0.02);
}

.item-image {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  color: white;
}

.status-badge.status-completed {
  background: #26de81;
}

.status-badge.status-registered {
  background: #667eea;
}

.status-badge.status-cancelled {
  background: #999;
}

.award-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.award-icon img {
  width: 48px;
  height: 48px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.item-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
  margin-bottom: 6px;
}

.item-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-item {
    padding: 12px 6px;
  }

  .stat-icon {
    width: 28px;
    height: 28px;
  }

  .stat-value {
    font-size: 16px;
  }

  .stat-label {
    font-size: 11px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .menu-item {
    padding: 14px 16px;
  }

  .menu-icon {
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }

  .menu-text {
    font-size: 15px;
  }

  .profile-card {
    padding: 24px 16px 16px;
  }

  .username {
    font-size: 20px;
  }

  .user-details {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-section {
  animation: fadeInUp 0.6s ease-out;
}

.stats-section {
  animation: fadeInUp 0.8s ease-out;
}

.menu-section {
  animation: fadeInUp 1s ease-out;
}

.content-section {
  animation: fadeInUp 1.2s ease-out;
}
</style>