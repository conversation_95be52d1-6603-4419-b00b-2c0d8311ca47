import { get } from "../../api/request";
import { VIDEO_URLS, ADVERTISE_URLS, HOSPITAL_NEWS_URLS, } from "../../api/urls";
/**
 * 获取列表
 * @param params 查询参数
 * @returns 列表数据
 */
export function getAdvertiseList(position) {
    return get(ADVERTISE_URLS.LIST, {
        position
    });
}
export function getHospitalNewsList(page = 1, pageSize = 4) {
    return get(HOSPITAL_NEWS_URLS.NEWS, {
        page,
        page_size: pageSize
    });
}
export function getVideoList(page = 1, pageSize = 6) {
    return get(VIDEO_URLS.VIDEO, {
        page,
        page_size: pageSize
    });
}
