import { ref, readonly, computed } from 'vue';
/**
 * 全局加载状态管理
 * 提供统一的加载状态管理，支持多个加载任务
 */
// 加载状态映射，key为加载任务ID，value为加载状态
const loadingMap = ref({});
// 获取只读的加载状态映射
export const loadingState = readonly(loadingMap);
/**
 * 设置加载状态
 * @param key 加载任务ID
 * @param status 加载状态
 */
export function setLoading(key, status) {
    loadingMap.value = { ...loadingMap.value, [key]: status };
}
/**
 * 获取加载状态
 * @param key 加载任务ID
 * @returns 加载状态
 */
export function getLoading(key) {
    return !!loadingMap.value[key];
}
/**
 * 获取全局加载状态
 * 当任意一个加载任务处于加载中状态时，全局加载状态为true
 * @returns 全局加载状态
 */
export function isLoading() {
    return Object.values(loadingMap.value).some(status => status);
}
/**
 * 清除加载状态
 * @param key 加载任务ID，不传则清除所有加载状态
 */
export function clearLoading(key) {
    if (key) {
        const newLoadingMap = { ...loadingMap.value };
        delete newLoadingMap[key];
        loadingMap.value = newLoadingMap;
    }
    else {
        loadingMap.value = {};
    }
}
/**
 * 加载状态包装器
 * 用于包装异步函数，自动管理加载状态
 * @param key 加载任务ID
 * @param fn 异步函数
 * @returns 包装后的异步函数
 */
export function withLoading(key, fn) {
    return async () => {
        try {
            setLoading(key, true);
            return await fn();
        }
        finally {
            setLoading(key, false);
        }
    };
}
/**
 * 创建加载状态钩子
 * 返回加载状态和加载状态包装器
 * @param key 加载任务ID
 * @returns [加载状态, 加载状态包装器]
 */
export function useLoading(key) {
    const loading = computed(() => getLoading(key));
    function withLoadingFn(fn) {
        return withLoading(key, fn);
    }
    return [loading, withLoadingFn];
}
// 导出默认对象
export default {
    setLoading,
    getLoading,
    isLoading,
    clearLoading,
    withLoading,
    useLoading,
    loadingState
};
