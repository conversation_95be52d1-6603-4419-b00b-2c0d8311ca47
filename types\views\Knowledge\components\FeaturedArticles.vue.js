import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SectionHeader from '../../../components/SectionHeader.vue';
import GridCard from '../../../components/GridCard.vue';
import { transformArticleListToGridCard, getCategoryRouteName, getCategoryBadge } from '../../../utils/articleTransform';
const router = useRouter();
const loading = ref(false);
const props = defineProps();
// 转换数据为GridCard格式
const cultureGridItems = computed(() => {
    return transformArticleListToGridCard(props.cultureData, getCategoryBadge('culture'));
});
const knowledgeGridItems = computed(() => {
    return transformArticleListToGridCard(props.knowledgeData, getCategoryBadge('knowledge'));
});
const caseGridItems = computed(() => {
    return transformArticleListToGridCard(props.caseData, getCategoryBadge('case'));
});
// 检查是否有文章
const hasArticles = computed(() => {
    return cultureGridItems.value.length > 0 ||
        knowledgeGridItems.value.length > 0 ||
        caseGridItems.value.length > 0;
});
// 处理卡片点击 - 根据原始数据判断分类
const handleCardClick = (item) => {
    console.log('点击了文章:', item);
    // 从原始数据中获取分类信息
    const originalData = item.originalData;
    // 根据数据来源确定分类和路由
    let category = '';
    if (props.cultureData.some(culture => culture.id === item.id)) {
        category = 'culture';
    }
    else if (props.knowledgeData.some(knowledge => knowledge.id === item.id)) {
        category = 'knowledge';
    }
    else if (props.caseData.some(caseItem => caseItem.id === item.id)) {
        category = 'case';
    }
    // 获取对应的路由名称
    const routeName = getCategoryRouteName(category);
    // 跳转到详情页
    router.push({ name: routeName, params: { id: item.id } });
};
// 组件挂载时打印数据（保持与原组件一致）
onMounted(() => {
    console.log('cultureData:', props.cultureData);
    console.log('knowledgeData:', props.knowledgeData);
    console.log('caseData:', props.caseData);
    console.log('转换后的GridCard数据:');
    console.log('- culture:', cultureGridItems.value);
    console.log('- knowledge:', knowledgeGridItems.value);
    console.log('- case:', caseGridItems.value);
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['articles-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
/** @type {__VLS_StyleScopedClasses['articles-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container featured-articles-section" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: "精品文章",
    icon: "description-o",
    showMore: (false),
}));
const __VLS_1 = __VLS_0({
    title: "精品文章",
    icon: "description-o",
    showMore: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_3 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
        size: "24px",
    }));
    const __VLS_5 = __VLS_4({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_4));
    __VLS_6.slots.default;
    var __VLS_6;
}
else if (!__VLS_ctx.hasArticles) {
    const __VLS_7 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        description: "暂无精品文章",
    }));
    const __VLS_9 = __VLS_8({
        description: "暂无精品文章",
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "articles-container" },
    });
    if (__VLS_ctx.cultureGridItems.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-section" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-title" },
        });
        const __VLS_11 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_12 = __VLS_asFunctionalComponent(__VLS_11, new __VLS_11({
            name: "gem-o",
            color: "#7c3aed",
            size: "16",
        }));
        const __VLS_13 = __VLS_12({
            name: "gem-o",
            color: "#7c3aed",
            size: "16",
        }, ...__VLS_functionalComponentArgsRest(__VLS_12));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        /** @type {[typeof GridCard, ]} */ ;
        // @ts-ignore
        const __VLS_15 = __VLS_asFunctionalComponent(GridCard, new GridCard({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.cultureGridItems),
        }));
        const __VLS_16 = __VLS_15({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.cultureGridItems),
        }, ...__VLS_functionalComponentArgsRest(__VLS_15));
        let __VLS_18;
        let __VLS_19;
        let __VLS_20;
        const __VLS_21 = {
            onCardClick: (__VLS_ctx.handleCardClick)
        };
        var __VLS_17;
    }
    if (__VLS_ctx.knowledgeGridItems.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-section" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-title" },
        });
        const __VLS_22 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_23 = __VLS_asFunctionalComponent(__VLS_22, new __VLS_22({
            name: "book-o",
            color: "#4b8bf4",
            size: "16",
        }));
        const __VLS_24 = __VLS_23({
            name: "book-o",
            color: "#4b8bf4",
            size: "16",
        }, ...__VLS_functionalComponentArgsRest(__VLS_23));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        /** @type {[typeof GridCard, ]} */ ;
        // @ts-ignore
        const __VLS_26 = __VLS_asFunctionalComponent(GridCard, new GridCard({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.knowledgeGridItems),
        }));
        const __VLS_27 = __VLS_26({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.knowledgeGridItems),
        }, ...__VLS_functionalComponentArgsRest(__VLS_26));
        let __VLS_29;
        let __VLS_30;
        let __VLS_31;
        const __VLS_32 = {
            onCardClick: (__VLS_ctx.handleCardClick)
        };
        var __VLS_28;
    }
    if (__VLS_ctx.caseGridItems.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-section" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "category-title" },
        });
        const __VLS_33 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_34 = __VLS_asFunctionalComponent(__VLS_33, new __VLS_33({
            name: "records",
            color: "#059669",
            size: "16",
        }));
        const __VLS_35 = __VLS_34({
            name: "records",
            color: "#059669",
            size: "16",
        }, ...__VLS_functionalComponentArgsRest(__VLS_34));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        /** @type {[typeof GridCard, ]} */ ;
        // @ts-ignore
        const __VLS_37 = __VLS_asFunctionalComponent(GridCard, new GridCard({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.caseGridItems),
        }));
        const __VLS_38 = __VLS_37({
            ...{ 'onCardClick': {} },
            items: (__VLS_ctx.caseGridItems),
        }, ...__VLS_functionalComponentArgsRest(__VLS_37));
        let __VLS_40;
        let __VLS_41;
        let __VLS_42;
        const __VLS_43 = {
            onCardClick: (__VLS_ctx.handleCardClick)
        };
        var __VLS_39;
    }
}
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['featured-articles-section']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['articles-container']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
/** @type {__VLS_StyleScopedClasses['category-section']} */ ;
/** @type {__VLS_StyleScopedClasses['category-title']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            GridCard: GridCard,
            loading: loading,
            cultureGridItems: cultureGridItems,
            knowledgeGridItems: knowledgeGridItems,
            caseGridItems: caseGridItems,
            hasArticles: hasArticles,
            handleCardClick: handleCardClick,
        };
    },
    __typeProps: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    __typeProps: {},
});
; /* PartiallyEnd: #4569/main.vue */
