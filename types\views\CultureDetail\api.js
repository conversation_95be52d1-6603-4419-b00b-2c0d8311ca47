import { get, requestWithRetry } from '../../api/request';
import { CULTURE_URLS, buildUrl } from '../../api/urls';
/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getCultureDetail(id) {
    return get(buildUrl(CULTURE_URLS.CULTURE, id));
}
/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getCultureDetailWithRetry(id) {
    return requestWithRetry(() => getCultureDetail(id));
}
