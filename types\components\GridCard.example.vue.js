import { ref } from 'vue';
import { showToast } from 'vant';
import GridCard from './GridCard.vue';
// 基础示例数据
const basicItems = ref([
    {
        id: 1,
        title: '名医讲堂：高血压的中医治疗方法',
        image: 'https://via.placeholder.com/300x200/4CAF50/white?text=高血压'
    },
    {
        id: 2,
        title: '中医育儿：小儿推拿手法',
        image: 'https://via.placeholder.com/300x200/2196F3/white?text=小儿推拿'
    },
    {
        id: 3,
        title: '急救常识：中医急救方法',
        image: 'https://via.placeholder.com/300x200/FF9800/white?text=急救'
    },
    {
        id: 4,
        title: '中医养生：四季养生之道',
        image: 'https://via.placeholder.com/300x200/9C27B0/white?text=养生'
    }
]);
// 带标识的示例数据
const badgeItems = ref([
    {
        id: 5,
        title: '最新医疗技术突破',
        image: 'https://via.placeholder.com/300x200/607D8B/white?text=医疗技术',
        badge: '推荐'
    },
    {
        id: 6,
        title: '健康饮食搭配指南',
        image: 'https://via.placeholder.com/300x200/4CAF50/white?text=健康饮食',
        badge: '热门'
    },
    {
        id: 7,
        title: '中医诊断学基础',
        image: 'https://via.placeholder.com/300x200/E91E63/white?text=中医诊断',
        badge: '精选'
    },
    {
        id: 8,
        title: '针灸治疗常见疾病',
        image: 'https://via.placeholder.com/300x200/3F51B5/white?text=针灸',
        badge: '新品'
    }
]);
// 处理卡片点击
const handleCardClick = (item) => {
    showToast(`点击了: ${item.title}`);
    console.log('卡片点击:', item);
};
// 处理图片错误
const handleImageError = (item) => {
    console.log('图片加载失败:', item);
    showToast('图片加载失败');
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "grid-card-example" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.section, __VLS_intrinsicElements.section)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof GridCard, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GridCard, new GridCard({
    ...{ 'onCardClick': {} },
    ...{ 'onImageError': {} },
    items: (__VLS_ctx.basicItems),
}));
const __VLS_1 = __VLS_0({
    ...{ 'onCardClick': {} },
    ...{ 'onImageError': {} },
    items: (__VLS_ctx.basicItems),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
const __VLS_7 = {
    onImageError: (__VLS_ctx.handleImageError)
};
var __VLS_2;
__VLS_asFunctionalElement(__VLS_intrinsicElements.section, __VLS_intrinsicElements.section)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof GridCard, ]} */ ;
// @ts-ignore
const __VLS_8 = __VLS_asFunctionalComponent(GridCard, new GridCard({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.badgeItems),
}));
const __VLS_9 = __VLS_8({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.badgeItems),
}, ...__VLS_functionalComponentArgsRest(__VLS_8));
let __VLS_11;
let __VLS_12;
let __VLS_13;
const __VLS_14 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
var __VLS_10;
__VLS_asFunctionalElement(__VLS_intrinsicElements.section, __VLS_intrinsicElements.section)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof GridCard, ]} */ ;
// @ts-ignore
const __VLS_15 = __VLS_asFunctionalComponent(GridCard, new GridCard({
    items: (__VLS_ctx.basicItems),
    clickable: (false),
}));
const __VLS_16 = __VLS_15({
    items: (__VLS_ctx.basicItems),
    clickable: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_15));
/** @type {__VLS_StyleScopedClasses['grid-card-example']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GridCard: GridCard,
            basicItems: basicItems,
            badgeItems: badgeItems,
            handleCardClick: handleCardClick,
            handleImageError: handleImageError,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
