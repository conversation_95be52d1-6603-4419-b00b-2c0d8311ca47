import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import { getHospitalNewsList } from '../api';
import SectionHeader from '../../../components/SectionHeader.vue';
import NewsCard from '../../../components/NewsCard.vue';
import { transformHospitalNewsListToNewsCard } from '../../../utils/newsTransform';
const router = useRouter();
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);
const pageSize = ref(4);
const finished = ref(false);
// 原始API数据
const items = ref([]);
// 转换为NewsCard组件需要的数据格式
const newsCardItems = computed(() => {
    return transformHospitalNewsListToNewsCard(items.value);
});
// 加载列表数据
const loadItems = async () => {
    if (loading.value)
        return;
    loading.value = true;
    try {
        const res = await getHospitalNewsList();
        console.log('获取到医院新闻列表数据:', res);
        // 检查响应数据结构
        if (!res || !res.results || !Array.isArray(res.results)) {
            console.error('API响应格式不正确:', res);
            items.value = [];
            return;
        }
        console.log('响应数据项数量:', res.results.length);
        // 直接使用后台返回的结果顺序，不进行前端排序
        items.value = res.results;
        console.log('设置的原始数据(按后台返回顺序):', items.value);
        console.log('转换后的NewsCard数据:', newsCardItems.value);
    }
    catch (err) {
        console.error('获取医院新闻失败:', err);
        error.value = "获取医院新闻失败，请稍后再试";
        showToast('获取新闻失败，请稍后重试');
    }
    finally {
        loading.value = false;
        refreshing.value = false;
    }
};
// 处理新闻卡片点击
const handleNewsClick = (item) => {
    console.log('点击了新闻:', item);
    router.push({ name: 'HospitalNewsDetail', params: { id: item.id } });
};
// 初始化
onMounted(() => {
    loadItems();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['news-list']} */ ;
/** @type {__VLS_StyleScopedClasses['news-list']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: "医院新闻",
    icon: "description-o",
    moreLink: "/hospital-news-list",
    showMore: (true),
}));
const __VLS_1 = __VLS_0({
    title: "医院新闻",
    icon: "description-o",
    moreLink: "/hospital-news-list",
    showMore: (true),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
if (__VLS_ctx.loading && !__VLS_ctx.newsCardItems.length) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_3 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
        size: "24px",
    }));
    const __VLS_5 = __VLS_4({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_4));
    __VLS_6.slots.default;
    var __VLS_6;
}
else if (!__VLS_ctx.newsCardItems.length) {
    const __VLS_7 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        description: "暂无新闻",
    }));
    const __VLS_9 = __VLS_8({
        description: "暂无新闻",
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "news-list" },
    });
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.newsCardItems))) {
        /** @type {[typeof NewsCard, ]} */ ;
        // @ts-ignore
        const __VLS_11 = __VLS_asFunctionalComponent(NewsCard, new NewsCard({
            ...{ 'onClick': {} },
            key: (item.id),
            newsItem: (item),
        }));
        const __VLS_12 = __VLS_11({
            ...{ 'onClick': {} },
            key: (item.id),
            newsItem: (item),
        }, ...__VLS_functionalComponentArgsRest(__VLS_11));
        let __VLS_14;
        let __VLS_15;
        let __VLS_16;
        const __VLS_17 = {
            onClick: (__VLS_ctx.handleNewsClick)
        };
        var __VLS_13;
    }
}
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-list']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            NewsCard: NewsCard,
            loading: loading,
            newsCardItems: newsCardItems,
            handleNewsClick: handleNewsClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
