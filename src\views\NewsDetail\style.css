.news-detail-page {
  padding: 0 0 20px 0;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-container p {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.error-container {
  padding: 30px 15px;
}

.content {
  padding: 5px;
}

/* 文章标题样式 */
.news-title {
  font-size: 22px;
  font-weight: bold;
  margin: 0 0 16px 0;
  color: #333;
  line-height: 1.4;
  text-align: left;
  word-break: break-word;
}

/* 文章标签样式 */
.news-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.news-tags .van-tag {
  font-size: 12px;
  border-radius: 16px;
  padding: 6px 12px;
  font-weight: 600;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.news-tags .van-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.news-tags .van-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.news-tags .van-tag:hover::before {
  left: 100%;
}

/* 分类标签 - 明艳蓝色 */
.news-tags .van-tag--primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* API标签1 - 明艳紫色 */
.news-tags .van-tag--default {
  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* API标签2 - 明艳绿色 */
.news-tags .van-tag--success {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}



/* API标签4/最新标签 - 明艳橙色 */
.news-tags .van-tag--warning {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c69 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 热门标签 - 明艳红色 */
.news-tags .van-tag--danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

/* 热门标签脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  }
}

/* 文章元信息样式 */
.news-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1989fa;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.meta-item .van-icon {
  margin-right: 6px;
  color: #1989fa;
  font-size: 16px;
}

.meta-label {
  margin-right: 4px;
  color: #666;
}

.meta-value {
  color: #333;
  font-weight: 500;
}

/* 文章封面图容器 */
.news-image-container {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

/* 文章摘要样式 */
.news-summary {
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 0;
  border: 1px solid #e1f0ff;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.summary-content {
  color: #666;
  line-height: 1.6;
  font-size: 15px;
  margin: 0;
  text-align: justify;
}

/* 正文内容样式 */
.news-content {
  color: #333;
  line-height: 1.8;
  font-size: 15px;
}

.news-content img {
  max-width: 100%;
  border-radius: 4px;
  margin: 15px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-content p {
  margin-bottom: 16px;
  text-align: justify;
  text-indent: 2em; /* 段落首行缩进 */
}

.news-content h1,
.news-content h2,
.news-content h3,
.news-content h4,
.news-content h5,
.news-content h6 {
  margin: 24px 0 12px 0;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
}

.news-content h1 {
  font-size: 20px;
  border-bottom: 2px solid #1989fa;
  padding-bottom: 8px;
}

.news-content h2 {
  font-size: 18px;
  color: #1989fa;
}

.news-content h3 {
  font-size: 16px;
}

.news-content ul,
.news-content ol {
  margin: 12px 0;
  padding-left: 24px;
}

.news-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.news-content blockquote {
  margin: 16px 0;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-left: 4px solid #1989fa;
  border-radius: 0 8px 8px 0;
  color: #666;
  font-style: italic;
  position: relative;
}

.news-content blockquote::before {
  content: '"';
  position: absolute;
  top: -8px;
  left: 12px;
  font-size: 36px;
  color: #1989fa;
  opacity: 0.3;
}

.news-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.news-content th,
.news-content td {
  border: 1px solid #e0e0e0;
  padding: 12px;
  text-align: left;
}

.news-content th {
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4f8 100%);
  font-weight: 600;
  color: #333;
}

.news-content td {
  background-color: #fff;
}

.news-content tr:nth-child(even) td {
  background-color: #fafbfc;
}



/* 响应式优化 */
@media (max-width: 480px) {
  .news-title {
    font-size: 20px;
  }
  
  .news-image {
    height: 180px;
  }
  
  .news-content {
    font-size: 14px;
  }
  
  .news-content p {
    text-indent: 1.5em;
  }
  
  .news-meta {
    padding: 10px;
  }
  
  .meta-item {
    font-size: 13px;
  }
  
  .summary-content {
    font-size: 14px;
  }
}

@media (max-width: 320px) {
  .news-title {
    font-size: 18px;
  }
  
  .news-tags .van-tag {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .news-image {
    height: 160px;
  }
}
