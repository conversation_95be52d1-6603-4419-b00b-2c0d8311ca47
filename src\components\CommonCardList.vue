<template>
  <div class="common-card-list" ref="scrollContainerRef">
    <!-- 首次加载中 -->
    <div v-if="loading && !items.length" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <!-- 空状态 -->
    <van-empty v-else-if="!items.length" :description="emptyText" />

    <!-- 列表 -->
    <van-list v-else-if="useInfiniteScroll" v-model:loading="internalLoading" :finished="finished" finished-text="没有更多了"
      @load="onLoadMore" :immediate-check="false" :scroll-container="getScrollContainer">
      <div v-for="item in items" :key="item.id" class="news-card animate__animated animate__fadeInUp"
        @click="$emit('card-click', item)">
        <div class="card-image-container">
          <van-tag v-if="item.job" type="success" size="medium" class="news-tag" round>
            {{ item.job }}
          </van-tag>
          <van-tag v-if="item.cat_name" type="success" size="medium" class="news-tag" round>
            {{ item.cat_name }}
          </van-tag>
          <img :src="item.thumbnail" :alt="item.name" class="news-image" />
          <div class="image-overlay"></div>
        </div>

        <div class="card-content">
          <h3 class="news-title">{{ item.name }}</h3>
          <p class="news-description">{{ item.desc }}</p>
        </div>
        <div class="activity-meta">
          <div class="meta-item" v-if="item.startdate">
            <van-icon name="calendar-o" />
            <span>{{ item.startdate }} ~ {{ item.enddate }}</span>
          </div>
          <div class="meta-item" v-if="item.location">
            <van-icon name="location-o" />
            <span>{{ item.location }}</span>
          </div>
        </div>
        <div class="card-meta">
          <div class="meta-left" v-if="item.cat_display">
            <span class="category">{{ item.cat_display }}</span>
          </div>
          <div class="meta-left" v-if="item.isfree">
            <span class="category">{{ item.isfree }}</span>
          </div>
          <div class="meta-right" v-if="item.tags && item.tags.length">
            <div class="card-tags">
              <span v-for="(tag, index) in item.tags" :key="index" class="tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <!-- 非无限滚动模式 -->
    <div v-else>
      <div v-for="item in items" :key="item.id" class="news-card animate__animated animate__fadeInUp"
        @click="$emit('card-click', item)">
        <div class="card-image-container">
          <van-tag v-if="item.job" type="success" size="medium" class="news-tag" round>
            {{ item.job }}
          </van-tag>
          <van-tag v-if="item.cat_name" type="success" size="medium" class="news-tag" round>
            {{ item.cat_name }}
          </van-tag>
          <img :src="item.thumbnail" :alt="item.name" class="news-image" />
          <div class="image-overlay"></div>
        </div>

        <div class="card-content">
          <h3 class="news-title">{{ item.name }}</h3>
          <p class="news-description">{{ item.desc }}</p>
        </div>
        <div class="activity-meta">
          <div class="meta-item" v-if="item.startdate">
            <van-icon name="calendar-o" />
            <span>{{ item.startdate }} ~ {{ item.enddate }}</span>
          </div>
          <div class="meta-item" v-if="item.location">
            <van-icon name="location-o" />
            <span>{{ item.location }}</span>
          </div>
        </div>
        <div class="card-meta">
          <div class="meta-left" v-if="item.cat_display">
            <span class="category">{{ item.cat_display }}</span>
          </div>
          <div class="meta-left" v-if="item.isfree">
            <span class="category">{{ item.isfree }}</span>
          </div>
          <div class="meta-right" v-if="item.tags && item.tags.length">
            <div class="card-tags">
              <span v-for="(tag, index) in item.tags" :key="index" class="tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
const scrollContainerRef = ref<HTMLElement | null>(null);
const getScrollContainer = () => scrollContainerRef.value;

interface Props {
  items: any[];
  loading: boolean;
  finished?: boolean;
  emptyText?: string;
  useInfiniteScroll?: boolean;
}
onMounted(() => {
  // 等 DOM 挂载
  nextTick(() => {
    console.log('滚动容器:', scrollContainerRef.value);
  });
});
const props = withDefaults(defineProps<Props>(), {
  emptyText: '暂无内容',
  useInfiniteScroll: true,
  finished: false,
});

const emit = defineEmits(['load-more', 'card-click']);

const internalLoading = ref(false);

watch(
  () => props.loading,
  (val) => {
    internalLoading.value = val;
  },
  { immediate: true }
);

const onLoadMore = () => {
  console.log('触发加载更多');
  emit('load-more');
};

const resetScroll = () => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.scrollTop = 0;
  }
};

defineExpose({
  resetScroll
});
</script>

<style scoped>
.common-card-list {
  flex: 1;
  overflow-y: auto;
}

.news-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.news-card {
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 8px;
  cursor: pointer;
  border: 1px solid rgba(75, 139, 244, 0.1);
  padding: 10px;
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(75, 139, 244, 0.15);
  border-color: rgba(75, 139, 244, 0.2);
}

.card-image-container {
  position: relative;
}

.news-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.news-card:hover .news-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent);
}

.card-content {
  padding: 8px;
  margin-bottom: 0;
}

.news-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.news-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 0px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.activity-meta {
  margin-bottom: 0px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
}

.meta-item .van-icon {
  color: #4b8bf4;
  font-size: 14px;
}

.loading-container {
  padding: 40px 0;
  text-align: center;
  color: #666;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  flex-wrap: wrap;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.meta-left {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.meta-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.category {
  color: #4b8bf4;
  font-weight: 500;
  background: rgba(75, 139, 244, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: rgba(16, 172, 132, 0.1);
  color: #10ac84;
  font-size: 10px;
  border-radius: 8px;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .news-image {
    height: 140px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .news-title {
    font-size: 15px;
  }
  
  .news-description {
    font-size: 13px;
  }
  
  .meta-item {
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .news-card,
  .news-image {
    transition: none;
  }

  .animate__fadeInUp {
    animation: none;
  }
}
</style>
