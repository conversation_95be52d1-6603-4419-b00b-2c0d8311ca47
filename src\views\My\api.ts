import { get, post } from '../../api/request';

// 用户信息接口
export interface UserInfo {
  id: number;
  name: string;
  phone: string;
  account: string;
  avatar: string;
  points: number;
}

// 用户活动接口
export interface UserActivity {
  id: number;
  name: string;
  thumbnail: string;
  startdate: string;
  status: string;
}

// 用户获奖接口
export interface UserAward {
  id: number;
  title: string;
  description: string;
  awardTime: string;
}

// 获取用户信息
export const getUserInfo = () => {
  return get<{ user: UserInfo }>('/api/user/info');
};

// 更新用户信息
export const updateUserInfo = (data: Partial<UserInfo>) => {
  return post<{ message: string }>('/api/user/update', data);
};

// 获取用户参与的活动
export const getUserActivities = () => {
  return get<{ activities: UserActivity[] }>('/api/user/activities');
};

// 获取用户获奖情况
export const getUserAwards = () => {
  return get<{ awards: UserAward[] }>('/api/user/awards');
};

// 获取用户积分明细
export const getUserPointsHistory = () => {
  return get<{ points: any[] }>('/api/user/points/history');
};