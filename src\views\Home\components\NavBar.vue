<template>
  <van-nav-bar 
    title="中医智慧" 
    fixed 
    placeholder 
    left-text="返回"
    right-text="搜索"
    @click-left="emit('left-click')"
    @click-right="emit('right-click')"
    class="custom-nav"
  >
    <template #title>
      <div class="nav-title">
        <span class="primary-title">中医智慧</span>
      </div>
    </template>
  </van-nav-bar>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

const emit = defineEmits(['left-click', 'right-click']);
</script>

<style scoped>
/* 导航栏样式 */
.custom-nav {
  --van-nav-bar-background: linear-gradient(135deg, #4b8bf4, #7cb9e8);
  --van-nav-bar-title-text-color: #fff;
  --van-nav-bar-text-color: #fff;
  --van-nav-bar-icon-color: #fff;
  width: 100% !important; /* 确保导航栏宽度为100% */
  box-sizing: border-box; /* 确保内边距和边框包含在宽度内 */
}

.nav-title {
  display: flex;
  /* flex-direction: column; */ /* No longer needed for single title */
  align-items: center;
  justify-content: center; /* Center the single title */
}

.primary-title {
  font-size: 18px; /* Matches knowledge page title */
  font-weight: bold; /* Matches knowledge page title (implicitly via font-weight: 600) */
}
</style>
