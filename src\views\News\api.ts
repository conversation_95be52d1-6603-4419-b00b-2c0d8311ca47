import { get, post, requestWithRetry } from '../../api/request';
import { HOSPITAL_NEWS_URLS, buildUrl } from '../../api/urls';
import type { HospitalNewsItem, HospitalNewsListResponse } from '../../types/news';


export function getHospitalNewsList(params?: any) {
  return get<HospitalNewsListResponse>(HOSPITAL_NEWS_URLS.NEWS, params);
}

export function getHospitalNewsListlWithRetry(params?: any) {
  return requestWithRetry<HospitalNewsListResponse>(() => getHospitalNewsList(params));
}