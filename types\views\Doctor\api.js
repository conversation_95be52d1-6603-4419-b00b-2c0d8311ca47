import { get, requestWithRetry } from '../../api/request';
import { FAMOUS_DOCTOR_URLS } from '../../api/urls';
/**
 * 获取领导分类
 * @returns 领导分类数据
 */
export function getDoctorCategories() {
    return get(FAMOUS_DOCTOR_URLS.CATEGORIES);
}
/**
 * 根据分类获取领导列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 领导列表数据
 */
export function getDoctorsByCategory(cat, page = 1, pageSize = 6) {
    return get(FAMOUS_DOCTOR_URLS.DOCTOR, {
        cat,
        page,
        page_size: pageSize
    });
}
/**
 * 带重试功能的获取领导分类
 * 在网络不稳定情况下使用
 * @returns 领导分类数据
 */
export function getDoctorCategoriesWithRetry(cat, page = 1, pageSize = 6) {
    return requestWithRetry(() => getDoctorsByCategory(cat, page, pageSize));
}
