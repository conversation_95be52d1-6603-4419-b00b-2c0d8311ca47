<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.2"/>
    </linearGradient>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
    <linearGradient id="tcmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#26de81"/>
      <stop offset="100%" style="stop-color:#4caf50"/>
    </linearGradient>
  </defs>

  <!-- 背景圆 -->
  <circle cx="100" cy="100" r="100" fill="url(#bgGradient)" stroke="#667eea" stroke-width="2" opacity="0.8"/>

  <!-- 装饰性太极图案 -->
  <g transform="translate(140, 40) scale(0.3)" opacity="0.3">
    <circle cx="0" cy="0" r="20" fill="#667eea"/>
    <path d="M 0,-20 A 10,10 0 0,1 0,0 A 10,10 0 0,0 0,20 A 20,20 0 0,1 0,-20" fill="white"/>
    <circle cx="0" cy="-10" r="3" fill="white"/>
    <circle cx="0" cy="10" r="3" fill="#667eea"/>
  </g>

  <!-- 头部 -->
  <circle cx="100" cy="75" r="35" fill="url(#avatarGradient)" stroke="white" stroke-width="3"/>

  <!-- 身体 -->
  <path d="M100 110c-28 0-50 22-50 50v40h100v-40c0-28-22-50-50-50z" fill="url(#avatarGradient)" stroke="white" stroke-width="2"/>

  <!-- 中医标识 - 草药叶子 -->
  <g transform="translate(85, 60)">
    <path d="M15 8 Q20 5 25 8 Q22 12 20 15 Q18 12 15 8" fill="url(#tcmGradient)"/>
    <path d="M20 15 Q25 18 20 22 Q15 18 20 15" fill="url(#tcmGradient)"/>
    <line x1="20" y1="15" x2="20" y2="8" stroke="white" stroke-width="1.5"/>
  </g>

  <!-- 装饰性中药材图案 -->
  <g transform="translate(60, 160) scale(0.4)" opacity="0.4">
    <circle cx="0" cy="0" r="8" fill="#26de81"/>
    <circle cx="15" cy="-5" r="6" fill="#4caf50"/>
    <circle cx="10" cy="10" r="5" fill="#66bb6a"/>
  </g>
</svg>