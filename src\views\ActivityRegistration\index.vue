<template>
  <div class="activity-registration-page">
    <!-- 自定义导航栏 -->
    <div class="custom-header">
      <div class="header-content">
        <van-icon name="arrow-left" class="back-icon" @click="goBack" />
        <span class="header-title">活动报名</span>
        <div class="header-placeholder"></div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#4b8bf4" size="32px" />
      <p>加载中...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败" image="error">
        <template #description>
          <p>{{ error }}</p>
        </template>
        <van-button round type="primary" @click="fetchActivityDetail">重试</van-button>
      </van-empty>
    </div>

    <!-- 报名成功页面 -->
    <div v-else-if="registrationSuccess" class="success-page">
      <div class="success-container">
        <div class="success-header">
          <van-icon name="checked" class="success-icon" />
          <h2>报名成功！</h2>
          <p>您已成功报名参加此活动</p>
        </div>

        <!-- 报名信息卡片 -->
        <div class="registration-info-card">
          <h3>报名信息</h3>
          <div class="info-item">
            <span class="label">报名编号：</span>
            <span class="value">{{ registrationInfo.regno }}</span>
          </div>
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ registrationInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号：</span>
            <span class="value">{{ registrationInfo.phone }}</span>
          </div>
          <div class="info-item">
            <span class="label">年龄：</span>
            <span class="value">{{ registrationInfo.age }}岁</span>
          </div>
          <div class="info-item">
            <span class="label">报名时间：</span>
            <span class="value">{{ formatDateTime(registrationInfo.create_time) }}</span>
          </div>
        </div>

        <!-- 二维码卡片 -->
        <div class="qrcode-card">
          <h3>活动入场码</h3>
          <p>请保存此二维码，活动当天凭此码入场</p>
          <div class="qrcode-container">
            <img :src="registrationInfo.qrcode" alt="活动二维码" class="qrcode-image" />
          </div>
          <!-- <div class="qrcode-actions">
            <van-button type="primary" @click="saveQRCode">保存二维码</van-button>
            <van-button type="default" @click="shareQRCode">分享</van-button>
          </div> -->
        </div>

        <!-- 温馨提示 -->
        <div class="tips-card">
          <h3>温馨提示</h3>
          <ul>
            <li>请妥善保存报名编号和二维码</li>
            <li>活动开始前30分钟开始入场</li>
            <li>请携带有效身份证件</li>
            <li>如有疑问请联系客服：400-123-4567</li>
          </ul>
        </div>

        <div class="success-actions">
          <van-button round block type="primary" @click="goBack">
            返回活动详情
          </van-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else class="content">
      <!-- 活动信息卡片 -->
      <div class="activity-info-card">
        <div class="activity-image-container">
          <img :src="activity.thumbnail" :alt="activity.name" class="activity-image" />
        </div>
        <div class="activity-info">
          <h2 class="activity-title">{{ activity.name }}</h2>
          <div class="activity-meta">
            <div class="meta-item">
              <van-icon name="clock-o" />
              <span>{{ formatActivityTimeRange(activity.startdate, activity.enddate) }}</span>
            </div>
            <div class="meta-item">
              <van-icon name="location-o" />
              <span>{{ activity.location || '待定' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 报名表单 -->
      <div class="registration-form-container">
        <div class="form-header">
          <h3>报名信息</h3>
          <p>请填写以下信息完成报名</p>
        </div>

        <van-form @submit="onSubmit" ref="formRef">
          <div class="form-section">
            <van-field v-model="formData.name" name="name" label="姓名" label-width="70px" placeholder="请输入您的真实姓名"
              :rules="[{ required: true, message: '请填写姓名' }]" left-icon="user-o" clearable />

            <van-field v-model="formData.phone" name="mobile" label="手机号" label-width="70px" placeholder="请输入手机号码" :rules="[
              { required: true, message: '请填写手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
            ]" left-icon="phone-o" clearable />

            <van-field v-model="formData.verificationCode" name="verificationCode" label="验证码" label-width="70px" placeholder="请输入短信验证码"
              :rules="[{ required: true, message: '请输入验证码' }]" left-icon="shield-o" clearable >
              <template #button>
                <van-button size="small" type="primary" :disabled="!canSendSms || smsCountdown > 0"
                  :loading="sendingSms" @click="sendSmsCode">
                  {{ getSmsButtonText() }}
                </van-button>
              </template>
            </van-field>

            <van-field v-model="formData.age" name="age" label="年龄" label-width="70px" placeholder="请输入年龄" type="number" :rules="[
              { required: true, message: '请填写年龄' },
              { pattern: /^(?:[1-9][0-9]?|1[01][0-9]|120)$/, message: '请输入正确的年龄(1-120)' }
            ]" left-icon="friends-o" clearable />
          </div>

          <!-- 协议同意 -->
          <div class="agreement-section">
            <van-checkbox v-model="formData.agreeTerms">
              我已阅读并同意
              <span class="agreement-link" @click="showAgreement">《活动参与协议》</span>
            </van-checkbox>
          </div>

          <!-- 提交按钮 -->
          <div class="submit-section">
            <van-button round block type="primary" native-type="submit" :loading="submitting"
              :disabled="!formData.agreeTerms" loading-text="提交中..." class="submit-btn">
              确认报名
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
    <!-- 协议弹窗 -->
    <van-popup v-model:show="showAgreementPopup" position="bottom" :style="{ height: '70%' }">
      <div class="agreement-popup">
        <div class="popup-header">
          <h3>活动参与协议</h3>
          <van-icon name="cross" @click="showAgreementPopup = false" />
        </div>
        <div class="popup-content">
          <div class="agreement-content">
            <h4>一、活动参与条件</h4>
            <p>1. 参与者须年满18周岁，具有完全民事行为能力；</p>
            <p>2. 参与者身体健康，无传染性疾病；</p>
            <p>3. 参与者须如实填写报名信息。</p>

            <h4>二、活动安全</h4>
            <p>1. 参与者须遵守活动现场的安全规定；</p>
            <p>2. 如有身体不适，请及时告知工作人员；</p>
            <p>3. 活动期间请保管好个人物品。</p>

            <h4>三、免责声明</h4>
            <p>1. 参与者因个人原因造成的损失，主办方不承担责任；</p>
            <p>2. 不可抗力因素导致活动取消或延期，主办方不赔偿责任；</p>
            <p>3. 活动最终解释权归主办方所有。</p>

            <h4>四、个人信息保护</h4>
            <p>1. 主办方将严格保护参与者的个人信息；</p>
            <p>2. 个人信息仅用于活动组织和联系；</p>
            <p>3. 未经同意，不会向第三方泄露个人信息。</p>
          </div>
        </div>
        <div class="popup-footer">
          <van-button round block type="primary" @click="agreeAndClose">
            我已阅读并同意
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast, showDialog, Toast } from 'vant';
import { getActivityDetail } from '../Activity/api';
import type { ActivityDetailItem } from '../Activity/api';
import { postActivityRegistration, postSMSCodeSend } from './api';
import type { ActivityRegistrationRequest } from './api';
import { formatActivityTimeRange, formatDateTime } from '../../utils/dateTime';
import 'vant/es/toast/style';  // ✅ 必须引入 Toast 样式



const router = useRouter();
const route = useRoute();
const loading = ref(false);
const error = ref('');
const submitting = ref(false);
const showAgreementPopup = ref(false);
const formRef = ref();

// 短信验证相关
const sendingSms = ref(false);
const smsCountdown = ref(0);
const smsTimer = ref<NodeJS.Timeout | null>(null);

// 报名成功状态
const registrationSuccess = ref(false);
const registrationInfo = ref({
  id: 0,
  regno: '',
  name: '',
  phone: '',
  age: '',
  create_time: '',
  qrcode: ''
});

// 表单数据类型
interface RegistrationFormData {
  name: string;
  phone: string;
  verificationCode: string;
  age: string;
  agreeTerms: boolean;
}

// 表单数据
const formData = ref<RegistrationFormData>({
  name: '',
  phone: '',
  verificationCode: '',
  age: '',
  agreeTerms: false
});

const goBack = () => {
  router.back();
};

// 活动数据
const activity = ref<ActivityDetailItem>({
  id: 0,
  name: '',
  cat: '',
  cat_name: '',
  desc: '',
  thumbnail: '',
  isfree: 0,
  startdate: '',
  enddate: '',
  views: 0,
  location: '',
  content: '',
});

// 获取活动详情
const fetchActivityDetail = async () => {
  const id = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
  console.log('获取活动详情，ID:', id);

  if (!id) {
    error.value = '活动ID无效';
    return;
  }

  try {
    loading.value = true;
    const res = await getActivityDetail(id);
    console.log('获取到活动详情数据:', res);
    const data = res.activity;

    activity.value = {
      id: data.id,
      name: data.name,
      cat: data.cat,
      cat_name: data.cat_name,
      desc: data.desc,
      startdate: data.startdate,
      enddate: data.enddate,
      location: data.location,
      thumbnail: data.thumbnail,
      views: data.views,
      isfree: data.isfree,
      content: data.content || '',
    };

  } catch (err) {
    console.error('获取活动详情失败:', err);
    error.value = '获取活动详情失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};



// 判断是否可以发送短信
const canSendSms = computed(() => {
  return /^1[3-9]\d{9}$/.test(formData.value.phone);
});

// 获取短信按钮文本
const getSmsButtonText = () => {
  if (smsCountdown.value > 0) {
    return `${smsCountdown.value}s`;
  }
  return sendingSms.value ? '发送中' : '获取验证码';
};

// 发送短信验证码
const sendSmsCode = async () => {
  if (!canSendSms.value) {
    showToast({ type: 'fail', message: '请输入正确的手机号' });
    return;
  }

  try {
    sendingSms.value = true;

    // 调用真实 API - 发送验证码
    console.log('发送验证码到手机号:', formData.value.phone);
    const res = await postSMSCodeSend({ phone: formData.value.phone });

    if ('message' in res) {
      showToast({ type: 'success', message: res.message });
    } else if ('error' in res) {
      showToast({ type: 'fail', message: res.error });
      return; // 不启动倒计时
    }

    // ✅ 成功后启动倒计时
    smsCountdown.value = 60;
    smsTimer.value = setInterval(() => {
      smsCountdown.value--;
      if (smsCountdown.value <= 0) {
        clearInterval(smsTimer.value!);
        smsTimer.value = null;
      }
    }, 1000);
  } catch (err) {
    console.error('发送短信失败:', err);
    showToast({ type: 'fail', message: '发送失败，请稍后再试' });
  } finally {
    sendingSms.value = false;
  }
};

// 显示协议
const showAgreement = () => {
  showAgreementPopup.value = true;
};

// 同意协议并关闭
const agreeAndClose = () => {
  formData.value.agreeTerms = true;
  showAgreementPopup.value = false;
  showToast({ type: 'success', message: '已同意协议' });

};

// 在报名成功的处理逻辑中添加跳转

// 表单提交
const onSubmit = async (values: any) => {
  submitting.value = true;
  console.log('提交报名信息:', values);

  try {
    // 表单校验
    await formRef.value?.validate();

    // 验证手机号和验证码是否填写
    if (!formData.value.phone || !values.verificationCode) {
      showToast({ type: 'fail', message: '手机号和验证码不能为空' });
      return;
    }

    // 构造请求参数 - 按照后端要求的字段名传递数据
    const requestParams: ActivityRegistrationRequest = {
      activity: activity.value.id,
      name: values.name,
      phone: formData.value.phone,  // 使用formData中的phone值，映射到phone字段
      code: values.verificationCode,  // 后端需要 code 字段用于验证码校验
      age: values.age,
    };

    console.log('发送报名请求参数:', requestParams);

    // 请求后端接口
    const res = await postActivityRegistration(requestParams);

    console.log('报名接口返回:', res);

    // 检查是否有错误响应
    if ('success' in res && res.success === false) {
      // 处理后端返回的错误信息
      let errorMessage = res.message || '报名失败';
      
      // 如果有详细的验证错误信息，显示具体错误
      if (res.errors) {
        const errorMessages = Object.entries(res.errors)
          .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
          .join('; ');
        errorMessage = errorMessages || errorMessage;
      }
      
      showToast({ type: 'fail', message: errorMessage });
      return;
    }

    // 获取成功响应的数据
    const reg = 'results' in res ? res.results : null;
    if (!reg) {
      throw new Error('报名失败：返回数据格式错误');
    }

    // 成功：赋值信息 + 切换视图
    registrationInfo.value = {
      id: reg.id || 0,
      regno: reg.regno || '',
      name: reg.name || '',
      phone: reg.phone || '',
      age: reg.age || '',
      create_time: reg.create_time || '',
      qrcode: reg.qrcode || '',
    };

    // 保存用户信息到本地存储
    const userInfo = {
      name: reg.name,
      phone: reg.phone,
      account: reg.phone, // 使用手机号作为账号
      avatar: '',
      points: 10 // 报名成功奖励10积分
    };
    localStorage.setItem('userInfo', JSON.stringify(userInfo));

    registrationSuccess.value = true;
    showToast({ type: 'success', message: '报名成功！' });
    
    // 延迟2秒后自动跳转到"我的"页面
    setTimeout(() => {
      router.push('/my');
    }, 2000);
    
    console.log('报名成功:', reg);
  } catch (err: any) {
    console.error('报名失败:', err);
    
    // 处理网络错误或其他异常
    let errorMessage = '报名失败，请稍后再试';
    
    if (err?.response?.data) {
      const errorData = err.response.data;
      if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.errors) {
        const errorMessages = Object.values(errorData.errors).flat().join(', ');
        errorMessage = errorMessages;
      }
    } else if (err?.message) {
      errorMessage = err.message;
    }
    
    showToast({ type: 'fail', message: errorMessage });
  } finally {
    submitting.value = false;
  }
};


// 清理定时器
const cleanup = () => {
  if (smsTimer.value) {
    clearInterval(smsTimer.value);
    smsTimer.value = null;
  }
};

// 初始化
onMounted(() => {
  fetchActivityDetail();
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style src="./style.css"></style>