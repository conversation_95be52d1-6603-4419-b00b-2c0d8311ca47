import { defineEmits } from 'vue';
const emit = defineEmits(['left-click', 'right-click']);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
const __VLS_0 = {}.VanNavBar;
/** @type {[typeof __VLS_components.VanNavBar, typeof __VLS_components.vanNavBar, typeof __VLS_components.VanNavBar, typeof __VLS_components.vanNavBar, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    ...{ 'onClickLeft': {} },
    ...{ 'onClickRight': {} },
    title: "中医智慧",
    fixed: true,
    placeholder: true,
    leftText: "返回",
    rightText: "搜索",
    ...{ class: "custom-nav" },
}));
const __VLS_2 = __VLS_1({
    ...{ 'onClickLeft': {} },
    ...{ 'onClickRight': {} },
    title: "中医智慧",
    fixed: true,
    placeholder: true,
    leftText: "返回",
    rightText: "搜索",
    ...{ class: "custom-nav" },
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
let __VLS_4;
let __VLS_5;
let __VLS_6;
const __VLS_7 = {
    onClickLeft: (...[$event]) => {
        __VLS_ctx.emit('left-click');
    }
};
const __VLS_8 = {
    onClickRight: (...[$event]) => {
        __VLS_ctx.emit('right-click');
    }
};
var __VLS_9 = {};
__VLS_3.slots.default;
{
    const { title: __VLS_thisSlot } = __VLS_3.slots;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "nav-title" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "primary-title" },
    });
}
var __VLS_3;
/** @type {__VLS_StyleScopedClasses['custom-nav']} */ ;
/** @type {__VLS_StyleScopedClasses['nav-title']} */ ;
/** @type {__VLS_StyleScopedClasses['primary-title']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            emit: emit,
        };
    },
    emits: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
});
; /* PartiallyEnd: #4569/main.vue */
