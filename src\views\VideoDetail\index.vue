<template>
  <div class="video-promotion-detail-container">
    <GlobalHeader title="视频详情" @left-click="onClickLeft" />

    <div v-if="!loading && !error && videoDetail" class="detail-content">
      <!-- 视频播放器区域 -->
      <!-- 视频播放器区域 -->
      <div class="video-player">
        <div class="video-wrapper">
          <!-- 如果能识别出腾讯视频 VID，使用 iframe 播放 -->
          <iframe v-if="tencentVid" class="tencent-video-iframe"
            :src="`https://v.qq.com/txp/iframe/player.html?vid=${tencentVid}`" frameborder="0" allowfullscreen></iframe>

          <!-- 如果不是腾讯视频链接，使用原生 video 播放 -->
          <template v-else>
            <div v-if="!isPlaying" class="video-placeholder" @click="playVideo">
              <img :src="videoDetail.thumbnail" :alt="videoDetail.name" class="video-thumbnail" />
              <div class="play-button">
                <van-icon name="play" size="48" color="white" />
              </div>
              <div class="video-duration">{{ videoDetail.duration }}</div>
            </div>
            <video v-else ref="videoRef" class="video-element" controls autoplay :src="videoDetail.video"
              @ended="isPlaying = false"></video>
          </template>
        </div>
      </div>


      <!-- 视频基本信息 -->
      <div class="detail-header">
        <h1 class="detail-title">{{ videoDetail.name }}</h1>
        <div class="detail-meta">
          <span class="author">
            <van-icon name="user-o" size="12" />
            {{ videoDetail.creater }}
          </span>
          <span class="date">
            <van-icon name="clock-o" size="12" />
            {{ formatDate(videoDetail.create_time) }}
          </span>
          <!-- <span class="views">
            <van-icon name="eye-o" size="12" />
            {{ videoDetail.views }}
          </span> -->
        </div>
      </div>

      <!-- 视频描述 -->
      <div class="detail-body">
        <!-- 标签区域 -->
        <div class="content-section" v-if="videoDetail.tags && videoDetail.tags.length">
          <h3>
            <van-icon name="tag-o" size="18" color="#1989fa" />
            标签
          </h3>
          <div class="tags-container">
            <van-tag 
              v-for="(tag, index) in videoDetail.tags" 
              :key="index"
              size="large"
              round
              :class="`tag-item ${getTagColorClass(index)}`"
            >
              {{ tag }}
            </van-tag>
          </div>
        </div>

        <div class="content-section">
          <h3>
            <van-icon name="info-o" size="18" color="#f5a623" />
            视频简介
          </h3>
          <p>{{ videoDetail.desc }}</p>
        </div>

        <div class="content-section">
          <h3>
            <van-icon name="notes-o" size="18" color="#722ed1" />
            详细介绍
          </h3>
          <div v-html="videoDetail.content"></div>
        </div>

      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#1989fa" />
      <p>加载中...</p>
    </div>

    <!-- 错误状态 -->
    <van-empty v-if="!loading && error" :description="error" />

    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import { getVideoDetail } from './api'; // 请确保你有此接口
import type { VideoDetail } from './api';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const error = ref("");
const isPlaying = ref(false);
const videoRef = ref<HTMLVideoElement | null>(null);

// 提取腾讯视频链接中的 vid
const getTencentVid = (url: string): string | null => {
  const match = url.match(/\/page\/([a-zA-Z0-9]+)\.html/);
  return match ? match[1] : null;
};

// 如果是腾讯视频链接，提取 vid
const tencentVid = computed(() => {
  if (videoDetail.value?.video?.includes("v.qq.com/x/page/")) {
    return getTencentVid(videoDetail.value.video);
  }
  return null;
});

// 获取标签颜色类名
const getTagColorClass = (index: number) => {
  const colorClasses = [
    'tag-red',
    'tag-orange', 
    'tag-yellow',
    'tag-green',
    'tag-cyan',
    'tag-blue',
    'tag-purple',
    'tag-pink',
    'tag-indigo',
    'tag-teal'
  ];
  return colorClasses[index % colorClasses.length];
};

const videoDetail = ref<VideoDetail | null>(null);

// 获取视频详情
const fetchVideoDetail = async () => {
  const id = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;

  if (!id) {
    error.value = "视频ID无效";
    return;
  }

  try {
    loading.value = true;
    const response = await getVideoDetail(id);

    const data = response.videos;
    if (!data) throw new Error("数据异常");

    // 调试输出：检查API返回的数据
    console.log('API返回的原始视频数据:', data);
    console.log('创作者字段:', data.creater);
    console.log('发布时间字段:', data.create_time);
    console.log('标签字段:', data.tags);

    videoDetail.value = {
      id: data.id,
      name: data.name,
      duration: "35:20",
      cat_display: data.cat_display,
      desc: data.desc,
      thumbnail: data.thumbnail.trim(),
      content: data.content,
      creater: data.creater || "未知创作者",
      create_time: data.create_time || new Date().toISOString().split('T')[0],
      video: data.video, // 默认视频链接
      tags: data.tags || [], // 从API获取tags，如果没有则为空数组
    };
    
    // 调试输出：检查处理后的数据
    console.log('处理后的视频详情数据:', videoDetail.value);
  } catch (e) {
    console.error(e);
    error.value = "获取视频失败";
  } finally {
    loading.value = false;
  }
};

// 格式化发布时间
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      return dateStr; // 如果无法解析，返回原始字符串
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.warn('时间格式化失败:', error);
    return dateStr;
  }
};

// 点击播放按钮
const playVideo = () => {
  isPlaying.value = true;
  nextTick(() => {
    videoRef.value?.play().catch(() => {
      showToast("无法播放视频");
    });
  });
};

// 返回上一页
const onClickLeft = () => {
  router.back();
};

onMounted(fetchVideoDetail);
</script>

<style scoped>
.tencent-video-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-promotion-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.detail-content {
  padding: 6px;
}

.video-player {
  background: black;
  margin-bottom: 20px;
  border-radius: 16px;
  overflow: hidden;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  /* 16:9 */
}

.video-placeholder,
.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.detail-header {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.detail-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.detail-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.detail-meta .author,
.detail-meta .date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-meta .van-icon {
  color: #999;
}

.detail-body {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  padding: 24px;
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.content-section {
  margin-bottom: 24px;
}

.content-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 12px;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-section p {
  color: #4a5568;
  line-height: 1.8;
  font-size: 15px;
  text-align: justify;
}

.content-section div {
  color: #4a5568;
  line-height: 1.8;
  font-size: 15px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #e2e8f0;
}

/* 标签颜色样式 */
.tag-blue {
  background: linear-gradient(135deg, #1989fa, #40a9ff);
  color: white;
}

.tag-red {
  background: linear-gradient(135deg, #f56565, #e53e3e);
  color: white;
}

.tag-orange {
  background: linear-gradient(135deg, #ff8c42, #ff7875);
  color: white;
}

.tag-yellow {
  background: linear-gradient(135deg, #ffd93d, #ffc53d);
  color: #333;
}

.tag-green {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
}

.tag-purple {
  background: linear-gradient(135deg, #9f7aea, #805ad5);
  color: white;
}

.tag-cyan {
  background: linear-gradient(135deg, #4ecdc4, #26d0ce);
  color: white;
}

.tag-pink {
  background: linear-gradient(135deg, #ff6b9d, #c44569);
  color: white;
}

.tag-indigo {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.tag-teal {
  background: linear-gradient(135deg, #4db6ac, #26a69a);
  color: white;
}

.loading-container {
  text-align: center;
  padding: 40px 0;
  color: #888;
}
</style>
