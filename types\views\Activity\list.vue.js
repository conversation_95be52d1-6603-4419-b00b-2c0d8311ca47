import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import ActivityHistoryCardList from '../../components/ActivityHistoryCardList.vue';
import { getActivityListlWithRetry, getCurrentMonthActivities, getPastActivities, getUpcomingActivities } from './api';
const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref([]);
const page = ref(1);
const pageSize = ref(10); // 每次加载10条数据
// 获取路由参数类型
const param = computed(() => router.currentRoute.value.query.type);
// 根据类型设置标题
const navTitle = computed(() => {
    switch (param.value) {
        case 'past':
            return '历史活动';
        case 'current':
            return '本月活动';
        case 'upcoming':
            return '活动预告';
        default:
            return '活动列表';
    }
});
// 获取空状态文本
const getEmptyText = () => {
    switch (param.value) {
        case 'past':
            return '暂无历史活动';
        case 'current':
            return '暂无本月活动';
        case 'upcoming':
            return '暂无活动预告';
        default:
            return '暂无活动数据';
    }
};
// 模拟历史活动数据
const getMockHistoryData = () => {
    return [
        {
            id: 1,
            name: '中医养生健康讲座：冬季进补指南',
            cat: 'health_lecture',
            cat_name: '健康讲座',
            desc: '本次讲座由资深中医专家为大家详细介绍冬季进补的原理、方法和注意事项，帮助大家科学养生，增强体质，预防疾病。',
            thumbnail: 'https://picsum.photos/400/300?random=1',
            isfree: 1,
            startdate: '2024-01-15',
            enddate: '2024-01-15',
            views: 1256,
            location: '医院三楼学术报告厅',
            tags: ['中医', '养生', '冬季进补']
        },
        {
            id: 2,
            name: '针灸技术培训班：传统医学实践',
            cat: 'professional_training',
            cat_name: '专业培训',
            desc: '面向医务人员的针灸技术专业培训，由国家级名老中医亲自授课，理论与实践相结合，提升临床技能。',
            thumbnail: 'https://picsum.photos/400/300?random=2',
            isfree: 0,
            startdate: '2024-01-20',
            enddate: '2024-01-22',
            views: 856,
            location: '中医技能培训中心',
            tags: ['针灸', '培训', '中医技术']
        },
        {
            id: 3,
            name: '中药材识别与应用科普活动',
            cat: 'science_education',
            cat_name: '科普教育',
            desc: '带领市民了解常见中药材的识别方法、功效作用和日常应用，增进对中医药文化的理解和认识。',
            thumbnail: 'https://picsum.photos/400/300?random=3',
            isfree: 1,
            startdate: '2024-02-10',
            enddate: '2024-02-10',
            views: 2341,
            location: '中药标本馆',
            tags: ['中药材', '科普', '传统文化']
        },
        {
            id: 4,
            name: '太极拳健身班：强身健体养心神',
            cat: 'fitness_activity',
            cat_name: '健身活动',
            desc: '专业太极拳教练指导，适合各年龄段人群参与，通过太极拳练习达到强身健体、修身养性的目的。',
            thumbnail: 'https://picsum.photos/400/300?random=4',
            isfree: 1,
            startdate: '2024-02-25',
            enddate: '2024-03-25',
            views: 1876,
            location: '医院健身广场',
            tags: ['太极拳', '健身', '养生']
        },
        {
            id: 5,
            name: '中医诊疗技术研讨会',
            cat: 'academic_conference',
            cat_name: '学术会议',
            desc: '邀请国内知名中医专家分享最新诊疗技术和临床经验，促进中医药学术交流与发展。',
            thumbnail: 'https://picsum.photos/400/300?random=5',
            isfree: 0,
            startdate: '2024-03-08',
            enddate: '2024-03-09',
            views: 945,
            location: '国际会议中心',
            tags: ['学术', '研讨会', '中医诊疗']
        },
        {
            id: 6,
            name: '春季养肝护肝健康讲座',
            cat: 'health_lecture',
            cat_name: '健康讲座',
            desc: '春季是养肝的最佳时节，专家将详细讲解春季养肝的重要性、方法和注意事项，帮助大家健康度过春季。',
            thumbnail: 'https://picsum.photos/400/300?random=6',
            isfree: 1,
            startdate: '2024-03-15',
            enddate: '2024-03-15',
            views: 1567,
            location: '医院门诊大厅',
            tags: ['春季养生', '养肝', '健康讲座']
        },
        {
            id: 7,
            name: '中医药文化展览活动',
            cat: 'cultural_activity',
            cat_name: '文化活动',
            desc: '通过图片、实物、互动体验等形式，全面展示中医药文化的深厚底蕴和现代发展成就。',
            thumbnail: 'https://picsum.photos/400/300?random=7',
            isfree: 1,
            startdate: '2024-03-20',
            enddate: '2024-03-30',
            views: 3245,
            location: '文化展览馆',
            tags: ['中医药文化', '展览', '传统文化']
        },
        {
            id: 8,
            name: '儿童推拿技术培训班',
            cat: 'professional_training',
            cat_name: '专业培训',
            desc: '专门针对儿童常见疾病的推拿技术培训，适合家长和医护人员参加，学习安全有效的儿童保健方法。',
            thumbnail: 'https://picsum.photos/400/300?random=8',
            isfree: 0,
            startdate: '2024-04-05',
            enddate: '2024-04-07',
            views: 1234,
            location: '儿科培训室',
            tags: ['儿童推拿', '培训', '儿童保健']
        }
    ];
};
// 模拟本月活动数据
const getMockCurrentData = () => {
    return [
        {
            id: 101,
            name: '冬季中医养生保健讲座',
            cat: 'health_lecture',
            cat_name: '健康讲座',
            desc: '专家讲解冬季养生要点，包括起居调养、饮食调理、运动保健等，帮助大家健康过冬。',
            thumbnail: 'https://picsum.photos/400/300?random=101',
            isfree: 1,
            startdate: '2024-12-20',
            enddate: '2024-12-20',
            views: 456,
            location: '医院学术报告厅',
            tags: ['冬季养生', '保健', '中医']
        },
        {
            id: 102,
            name: '中医体质辨识与调理',
            cat: 'health_consultation',
            cat_name: '健康咨询',
            desc: '通过专业的中医体质辨识，为每位参与者制定个性化的调理方案，提升身体健康水平。',
            thumbnail: 'https://picsum.photos/400/300?random=102',
            isfree: 1,
            startdate: '2024-12-22',
            enddate: '2024-12-22',
            views: 234,
            location: '中医体质辨识中心',
            tags: ['体质辨识', '个性化调理', '中医诊疗']
        },
        {
            id: 103,
            name: '八段锦健身操教学',
            cat: 'fitness_activity',
            cat_name: '健身活动',
            desc: '学习传统八段锦健身操，通过简单易学的动作达到强身健体、延年益寿的效果。',
            thumbnail: 'https://picsum.photos/400/300?random=103',
            isfree: 1,
            startdate: '2024-12-25',
            enddate: '2024-12-25',
            views: 678,
            location: '医院健身广场',
            tags: ['八段锦', '健身操', '传统运动']
        },
        {
            id: 104,
            name: '中医药膳制作体验',
            cat: 'practical_activity',
            cat_name: '实践活动',
            desc: '现场学习制作几种简单易做的中医药膳，了解药食同源的理念，掌握日常养生技巧。',
            thumbnail: 'https://picsum.photos/400/300?random=104',
            isfree: 0,
            startdate: '2024-12-28',
            enddate: '2024-12-28',
            views: 345,
            location: '中医药膳制作室',
            tags: ['药膳', '制作体验', '药食同源']
        },
        {
            id: 105,
            name: '年末健康体检活动',
            cat: 'health_checkup',
            cat_name: '健康体检',
            desc: '提供全面的中医健康体检服务，包括脉诊、舌诊、体质检测等，为新年健康打好基础。',
            thumbnail: 'https://picsum.photos/400/300?random=105',
            isfree: 0,
            startdate: '2024-12-30',
            enddate: '2024-12-31',
            views: 567,
            location: '体检中心',
            tags: ['健康体检', '中医诊断', '年末检查']
        }
    ];
};
// 模拟活动预告数据
const getMockUpcomingData = () => {
    return [
        {
            id: 201,
            name: '新年中医养生规划讲座',
            cat: 'health_lecture',
            cat_name: '健康讲座',
            desc: '新年伊始，专家为大家制定全年的中医养生计划，包括四季养生要点和个人调理方案。',
            thumbnail: 'https://picsum.photos/400/300?random=201',
            isfree: 1,
            startdate: '2025-01-05',
            enddate: '2025-01-05',
            views: 0,
            location: '医院大礼堂',
            tags: ['新年养生', '养生规划', '四季调理']
        },
        {
            id: 202,
            name: '春节期间饮食调理指导',
            cat: 'health_consultation',
            cat_name: '健康咨询',
            desc: '针对春节期间的饮食特点，提供专业的中医饮食调理建议，帮助大家健康过节。',
            thumbnail: 'https://picsum.photos/400/300?random=202',
            isfree: 1,
            startdate: '2025-01-15',
            enddate: '2025-01-15',
            views: 0,
            location: '营养咨询室',
            tags: ['春节饮食', '饮食调理', '节日养生']
        },
        {
            id: 203,
            name: '中医美容养颜技术培训',
            cat: 'professional_training',
            cat_name: '专业培训',
            desc: '学习中医美容养颜的理论和实践技术，包括面部按摩、穴位美容、中药面膜制作等。',
            thumbnail: 'https://picsum.photos/400/300?random=203',
            isfree: 0,
            startdate: '2025-01-20',
            enddate: '2025-01-22',
            views: 0,
            location: '美容培训中心',
            tags: ['中医美容', '养颜技术', '专业培训']
        },
        {
            id: 204,
            name: '立春养生文化节',
            cat: 'cultural_activity',
            cat_name: '文化活动',
            desc: '庆祝立春节气，举办中医养生文化展示、互动体验、专家义诊等丰富多彩的活动。',
            thumbnail: 'https://picsum.photos/400/300?random=204',
            isfree: 1,
            startdate: '2025-02-04',
            enddate: '2025-02-04',
            views: 0,
            location: '医院广场',
            tags: ['立春', '养生文化节', '节气养生']
        },
        {
            id: 205,
            name: '儿童春季调理与保健',
            cat: 'health_lecture',
            cat_name: '健康讲座',
            desc: '专门针对儿童春季常见问题的中医调理方法，帮助家长掌握儿童春季保健知识。',
            thumbnail: 'https://picsum.photos/400/300?random=205',
            isfree: 1,
            startdate: '2025-02-15',
            enddate: '2025-02-15',
            views: 0,
            location: '儿科诊疗中心',
            tags: ['儿童保健', '春季调理', '家长教育']
        },
        {
            id: 206,
            name: '中医药研究成果发布会',
            cat: 'academic_conference',
            cat_name: '学术会议',
            desc: '发布医院最新的中医药研究成果，邀请业内专家学者共同探讨中医药发展前景。',
            thumbnail: 'https://picsum.photos/400/300?random=206',
            isfree: 0,
            startdate: '2025-02-28',
            enddate: '2025-02-28',
            views: 0,
            location: '学术会议厅',
            tags: ['研究成果', '学术发布', '中医药发展']
        }
    ];
};
const loadItems = async () => {
    if (finished.value || loading.value)
        return;
    loading.value = true;
    try {
        const params = {
            page: page.value,
            page_size: pageSize.value,
            param: param.value // 传递活动类型参数
        };
        console.log(`加载${navTitle.value}数据，参数:`, params);
        // 根据类型调用对应的API
        let response;
        switch (param.value) {
            case 'past':
                response = await getPastActivities(params);
                break;
            case 'current':
                response = await getCurrentMonthActivities(params);
                break;
            case 'upcoming':
                response = await getUpcomingActivities(params);
                break;
            default:
                response = await getActivityListlWithRetry(params);
        }
        console.log(`${navTitle.value}API响应:`, response);
        // 添加新数据到列表
        items.value.push(...response.results);
        page.value += 1;
        // 检查是否已经加载完所有数据
        finished.value = response.is_last_page || response.results.length < pageSize.value;
    }
    catch (error) {
        console.error(`加载${navTitle.value}失败:`, error);
        showToast(`加载${navTitle.value}失败，请重试`);
        // 发生错误时使用模拟数据作为后备
        try {
            const mockData = getMockDataByType(param.value);
            const startIndex = (page.value - 1) * pageSize.value;
            const endIndex = startIndex + pageSize.value;
            const pageData = mockData.slice(startIndex, endIndex);
            if (pageData.length > 0) {
                items.value.push(...pageData);
                page.value += 1;
                finished.value = endIndex >= mockData.length;
                showToast('已加载离线数据');
            }
            else {
                finished.value = true;
            }
        }
        catch (mockError) {
            console.error('加载模拟数据也失败:', mockError);
            finished.value = true;
        }
    }
    finally {
        loading.value = false;
    }
};
// 根据类型获取模拟数据
const getMockDataByType = (type) => {
    switch (type) {
        case 'past':
            return getMockHistoryData();
        case 'current':
            return getMockCurrentData();
        case 'upcoming':
            return getMockUpcomingData();
        default:
            return getMockHistoryData();
    }
};
const handleCardClick = (item) => {
    console.log('点击活动卡片:', item);
    // 跳转到活动详情页
    router.push(`/activity-detail/${item.id}`);
};
// 重置数据
const resetData = () => {
    items.value = [];
    page.value = 1;
    finished.value = false;
    loading.value = false;
};
// 监听路由参数变化
watch(() => param.value, () => {
    console.log('活动类型参数变化:', param.value);
    resetData();
    loadItems();
}, { immediate: false });
onMounted(() => {
    console.log('ActivityList页面加载，类型:', param.value);
    loadItems();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-list-page" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: (__VLS_ctx.navTitle),
}));
const __VLS_1 = __VLS_0({
    title: (__VLS_ctx.navTitle),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-grid" },
});
/** @type {[typeof ActivityHistoryCardList, ]} */ ;
// @ts-ignore
const __VLS_3 = __VLS_asFunctionalComponent(ActivityHistoryCardList, new ActivityHistoryCardList({
    ...{ 'onLoadMore': {} },
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.items),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (true),
    emptyText: (__VLS_ctx.getEmptyText()),
    activityType: (__VLS_ctx.param),
}));
const __VLS_4 = __VLS_3({
    ...{ 'onLoadMore': {} },
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.items),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (true),
    emptyText: (__VLS_ctx.getEmptyText()),
    activityType: (__VLS_ctx.param),
}, ...__VLS_functionalComponentArgsRest(__VLS_3));
let __VLS_6;
let __VLS_7;
let __VLS_8;
const __VLS_9 = {
    onLoadMore: (__VLS_ctx.loadItems)
};
const __VLS_10 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
var __VLS_5;
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_11 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_12 = __VLS_11({}, ...__VLS_functionalComponentArgsRest(__VLS_11));
/** @type {__VLS_StyleScopedClasses['activity-list-page']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-grid']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            ActivityHistoryCardList: ActivityHistoryCardList,
            loading: loading,
            finished: finished,
            items: items,
            param: param,
            navTitle: navTitle,
            getEmptyText: getEmptyText,
            loadItems: loadItems,
            handleCardClick: handleCardClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
