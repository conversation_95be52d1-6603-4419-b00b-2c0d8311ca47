<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#26de81;stop-opacity:0.4"/>
    </linearGradient>
    <radialGradient id="circleGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:white;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:white;stop-opacity:0.05"/>
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="400" height="300" fill="url(#bgGradient)"/>
  
  <!-- 装饰性圆形 -->
  <circle cx="350" cy="50" r="30" fill="url(#circleGradient)"/>
  <circle cx="50" cy="250" r="40" fill="url(#circleGradient)"/>
  <circle cx="300" cy="200" r="25" fill="url(#circleGradient)"/>
  
  <!-- 太极图案装饰 -->
  <g transform="translate(320, 120) scale(0.6)" opacity="0.3">
    <circle cx="0" cy="0" r="25" fill="white" fill-opacity="0.2"/>
    <path d="M 0,-25 A 12.5,12.5 0 0,1 0,0 A 12.5,12.5 0 0,0 0,25 A 25,25 0 0,1 0,-25" fill="white" fill-opacity="0.4"/>
    <circle cx="0" cy="-12.5" r="4" fill="white" fill-opacity="0.2"/>
    <circle cx="0" cy="12.5" r="4" fill="white" fill-opacity="0.4"/>
  </g>
  
  <!-- 中药材装饰图案 -->
  <g transform="translate(80, 80) scale(0.8)" opacity="0.2">
    <!-- 草药叶子 -->
    <path d="M0 0 Q10 -5 20 0 Q15 8 10 12 Q5 8 0 0" fill="white"/>
    <path d="M10 12 Q20 15 10 25 Q0 15 10 12" fill="white"/>
    <line x1="10" y1="12" x2="10" y2="0" stroke="white" stroke-width="1"/>
  </g>
  
  <!-- 八卦图案装饰 -->
  <g transform="translate(150, 200) scale(0.5)" opacity="0.15">
    <polygon points="0,-20 17.32,-10 17.32,10 0,20 -17.32,10 -17.32,-10" fill="white"/>
    <polygon points="0,-10 8.66,-5 8.66,5 0,10 -8.66,5 -8.66,-5" fill="white" fill-opacity="0.5"/>
  </g>
  
  <!-- 波浪装饰 -->
  <path d="M0 150 Q100 130 200 150 T400 150" stroke="white" stroke-width="2" fill="none" opacity="0.3"/>
  <path d="M0 180 Q100 160 200 180 T400 180" stroke="white" stroke-width="1.5" fill="none" opacity="0.2"/>
</svg>
