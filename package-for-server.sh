#!/bin/bash

# 中医智慧前端项目打包脚本
echo "📦 打包中医智慧前端项目用于服务器部署"
echo "=========================================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取当前目录
CURRENT_DIR=$(pwd)
PROJECT_NAME="zyyfront"
PACKAGE_NAME="${PROJECT_NAME}-$(date +%Y%m%d-%H%M%S).tar.gz"

echo -e "${BLUE}当前目录: $CURRENT_DIR${NC}"

# 创建临时目录
TEMP_DIR="/tmp/${PROJECT_NAME}_package"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

echo -e "${BLUE}步骤1: 复制项目文件${NC}"

# 复制必要文件到临时目录
cp -r src "$TEMP_DIR/"
cp -r public "$TEMP_DIR/"
cp -r types "$TEMP_DIR/"
cp package.json "$TEMP_DIR/"
cp package-lock.json "$TEMP_DIR/" 2>/dev/null || true
cp tsconfig*.json "$TEMP_DIR/"
cp vite.config.ts "$TEMP_DIR/"
cp index.html "$TEMP_DIR/"

# 复制部署脚本
cp deploy-ubuntu.sh "$TEMP_DIR/" 2>/dev/null || echo "注意: deploy-ubuntu.sh 文件未找到"
cp deploy-project.sh "$TEMP_DIR/" 2>/dev/null || echo "注意: deploy-project.sh 文件未找到"
cp install-ssl.sh "$TEMP_DIR/" 2>/dev/null || echo "注意: install-ssl.sh 文件未找到"
cp nginx.conf "$TEMP_DIR/" 2>/dev/null || echo "注意: nginx.conf 文件未找到"
cp UBUNTU-DEPLOYMENT.md "$TEMP_DIR/" 2>/dev/null || echo "注意: UBUNTU-DEPLOYMENT.md 文件未找到"

# 复制其他配置文件
cp .gitignore "$TEMP_DIR/" 2>/dev/null || true
cp README.md "$TEMP_DIR/" 2>/dev/null || true

echo -e "${GREEN}✅ 文件复制完成${NC}"

# 创建部署说明文件
echo -e "${BLUE}步骤2: 创建部署说明${NC}"
cat > "$TEMP_DIR/DEPLOY_INSTRUCTIONS.md" << 'EOF'
# 中医智慧前端项目 - 服务器部署说明

## 🚀 快速部署步骤

### 1. 解压项目文件
```bash
tar -xzf zyyfront-*.tar.gz
cd zyyfront/
```

### 2. 安装服务器环境 (首次部署)
```bash
sudo bash deploy-ubuntu.sh
```

### 3. 构建和部署项目
```bash
bash deploy-project.sh
```

### 4. 配置SSL证书 (可选)
```bash
sudo bash install-ssl.sh
```

## 📋 详细说明
请查看 UBUNTU-DEPLOYMENT.md 文件获取完整部署指南。

## 🔧 手动部署
如果自动脚本失败，请按照 UBUNTU-DEPLOYMENT.md 中的详细步骤手动部署。

## 📞 技术支持
部署过程中如遇到问题，请查看日志文件：
- Nginx错误日志: /var/log/nginx/error.log
- 项目错误日志: /var/log/nginx/zyyfront_error.log
EOF

echo -e "${GREEN}✅ 部署说明创建完成${NC}"

# 打包项目
echo -e "${BLUE}步骤3: 压缩打包${NC}"
cd "$(dirname "$TEMP_DIR")"
tar -czf "$CURRENT_DIR/$PACKAGE_NAME" "$(basename "$TEMP_DIR")"

# 清理临时目录
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ 打包完成${NC}"
echo ""
echo -e "${YELLOW}打包信息:${NC}"
echo -e "文件名: $PACKAGE_NAME"
echo -e "大小: $(du -h "$CURRENT_DIR/$PACKAGE_NAME" | cut -f1)"
echo -e "位置: $CURRENT_DIR/$PACKAGE_NAME"

echo ""
echo -e "${YELLOW}上传到服务器:${NC}"
echo -e "scp $PACKAGE_NAME root@YOUR_SERVER_IP:/home/"

echo ""
echo -e "${YELLOW}服务器上部署:${NC}"
echo -e "ssh root@YOUR_SERVER_IP"
echo -e "cd /home && tar -xzf $PACKAGE_NAME"
echo -e "cd $(basename "$PACKAGE_NAME" .tar.gz) && sudo bash deploy-ubuntu.sh && bash deploy-project.sh"

echo ""
echo -e "${GREEN}🎉 项目打包完成！请将 $PACKAGE_NAME 文件上传到您的阿里云服务器${NC}" 