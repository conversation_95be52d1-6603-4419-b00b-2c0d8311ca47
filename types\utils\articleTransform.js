/**
 * 将API文章数据转换为GridCard组件需要的格式
 * @param item API文章数据
 * @param category 文章分类（可选）
 * @returns GridCard组件数据格式
 */
export function transformArticleToGridCard(item, category) {
    return {
        id: item.id,
        title: item.name,
        image: item.thumbnail,
        badge: category, // 可以根据分类设置标识
        // 保留原始数据以备扩展使用
        originalData: item
    };
}
/**
 * 批量转换文章数据
 * @param items API文章数据数组
 * @param category 文章分类
 * @returns GridCard组件数据格式数组
 */
export function transformArticleListToGridCard(items, category) {
    return items.map(item => transformArticleToGridCard(item, category));
}
/**
 * 根据分类获取对应的路由名称
 * @param category 分类类型
 * @returns 路由名称
 */
export function getCategoryRouteName(category) {
    const routeMap = {
        'culture': 'CultureDetail',
        'knowledge': 'TcmKnowledgeDetail',
        'case': 'CasesDetail'
    };
    return routeMap[category] || 'KnowledgeDetail';
}
/**
 * 根据分类获取标识文字
 * @param category 分类类型
 * @returns 标识文字
 */
export function getCategoryBadge(category) {
    const badgeMap = {
        'culture': '文化',
        'knowledge': '知识',
        'case': '案例'
    };
    return badgeMap[category] || '精选';
}
