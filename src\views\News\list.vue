<template>
  <div class="news-page">
    <GlobalHeader title="医院新闻" />
    <div class="section-container">
      <div class="news-grid">
        <CommonCardList :items="items" :loading="loading" :finished="finished" :use-infinite-scroll="true"
          @load-more="loadItems" @card-click="handleCardClick" />
      </div>
    </div>
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import CommonCardList from '../../components/CommonCardList.vue';
import { getHospitalNewsListlWithRetry } from './api'; // 假设支持分页参数
import type { HospitalNewsItem } from '../../types/news'; // 使用统一的类型定义

const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref<HospitalNewsItem[]>([]);
const page = ref(1);
const pageSize = ref(5); // 每次加载10条数据


const loadItems = async () => {
  if (finished.value || loading.value) return;

  loading.value = true;

  try {
    const res = await getHospitalNewsListlWithRetry({
      page: page.value,
      page_size: pageSize.value,
    }); 
    console.log('加载新闻数据:', res);
    console.log('is_last_page:', res.is_last_page);

    // 直接使用后台返回的结果顺序，不进行前端排序
    items.value.push(...res.results);
    page.value += 1;

    finished.value = res.is_last_page === true;

  } catch (error) {
    console.error('加载新闻失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleCardClick = (news: any) => {
  router.push({ name: 'HospitalNewsDetail', params: { id: news.id } });
};
onMounted(() => {
  loadItems();
});
</script>

<style scoped>
/* 你可以按需添加样式 */
.news-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.section-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.news-grid {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style>
