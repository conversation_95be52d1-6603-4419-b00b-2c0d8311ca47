<template>
  <div class="section-container">
    <div class="swipe-container animate__animated animate__fadeIn">
      <!-- 骨架屏 -->
      <div v-if="isLoading" class="skeleton-swipe">
        <div class="skeleton-item" v-for="i in 3" :key="i">
          <div class="skeleton-image"></div>
          <div class="skeleton-caption"></div>
        </div>
      </div>
      
      <!-- 轮播图 -->
      <div v-else-if="swipeImages.length > 0">
        <van-swipe 
          :autoplay="autoplay" 
          :show-indicators="false" 
          class="my-swipe"
          @change="onSwipeChange"
        >
          <van-swipe-item
            v-for="(item, index) in swipeImages"
            :key="item.id"
            @click="handleItemClick(item)"
          >
            <div class="swipe-item-content">
              <!-- 优化图片加载：首屏图片不使用懒加载，后续图片使用懒加载 -->
              <img
                v-if="index === 0"
                :src="getFullImageUrl(item.thumbnail)"
                class="swipe-image"
                :style="{ height }"
                :alt="item.name"
                @load="onImageLoad(index)"
                @error="onImageError(index)"
              />
              <img
                v-else
                v-lazy="getFullImageUrl(item.thumbnail)"
                class="swipe-image"
                :style="{ height }"
                :alt="item.name"
                @load="onImageLoad(index)"
                @error="onImageError(index)"
              />
              <div class="swipe-caption">{{ item.name }}</div>

              <!-- 图片加载状态指示器 -->
              <div v-if="imageLoadingStates[index]" class="image-loading">
                <van-loading type="spinner" color="#fff" />
              </div>
            </div>
          </van-swipe-item>
        </van-swipe>
        <div class="custom-indicator">
          <div 
            v-for="(_, index) in swipeImages" 
            :key="index" 
            :class="['indicator-dot', { active: currentSwipe === index }]"
          ></div>
        </div>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else class="no-data">
        <van-empty image="empty" description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineProps, defineEmits } from "vue";
import { getAdvertiseList } from "../api";
import type { AdvertiseItem } from "../api";
import { useRouter } from "vue-router";
import { showToast } from "vant";

const props = defineProps({
  position: {
    type: String,
    required: true,
    description: "广告位置标识，用于获取不同位置的广告数据",
  },
  autoplay: {
    type: Number,
    default: 3000,
    description: "轮播图自动切换时间间隔(ms)",
  },
  height: {
    type: String,
    default: "200px",
    description: "轮播图高度",
  },
});

const emits = defineEmits(["click"]);

const router = useRouter();

// 轮播图数据
const swipeImages = ref<AdvertiseItem[]>([]);

// 当前轮播图索引
const currentSwipe = ref(0);

// 加载状态
const isLoading = ref(true);

// 图片加载状态追踪
const imageLoadingStates = ref<boolean[]>([]);

// 图片URL处理 - 确保返回完整的URL
const getFullImageUrl = (thumbnail: string): string => {
  if (!thumbnail) return "";

  // 如果已经是完整URL，直接返回
  if (thumbnail.startsWith("http://") || thumbnail.startsWith("https://")) {
    return thumbnail;
  }

  // 如果是相对路径，添加基础域名
  const baseUrl =
    import.meta.env.MODE === "development"
      ? "https://houma.sxaliyun.cn"
      : "https://houma.sxaliyun.cn";

  // 确保路径以/开头
  const path = thumbnail.startsWith("/") ? thumbnail : `/${thumbnail}`;

  return `${baseUrl}${path}`;
};

// 图片加载成功事件
const onImageLoad = (index: number) => {
  imageLoadingStates.value[index] = false;
  console.log(`轮播图${index + 1}加载成功`);
};

// 图片加载失败事件
const onImageError = (index: number) => {
  imageLoadingStates.value[index] = false;
  console.error(`轮播图${index + 1}加载失败`);
};

// 预加载图片
const preloadImages = async (images: AdvertiseItem[]) => {
  console.log("开始预加载轮播图片...");

  // 初始化加载状态
  imageLoadingStates.value = new Array(images.length).fill(true);

  // 预加载第一张图片（最重要）
  if (images.length > 0) {
    const firstImage = new Image();
    firstImage.src = getFullImageUrl(images[0].thumbnail);
    firstImage.onload = () => {
      console.log("首张轮播图预加载完成");
      imageLoadingStates.value[0] = false;
    };
    firstImage.onerror = () => {
      console.error("首张轮播图预加载失败");
      imageLoadingStates.value[0] = false;
    };
  }

  // 预加载其他图片（低优先级）
  images.slice(1).forEach((item, index) => {
    setTimeout(() => {
      const img = new Image();
      img.src = getFullImageUrl(item.thumbnail);
      img.onload = () => {
        console.log(`轮播图${index + 2}预加载完成`);
      };
    }, (index + 1) * 200); // 延迟预加载，避免阻塞首张图片
  });
};

// 轮播图切换事件
const onSwipeChange = (index: number) => {
  currentSwipe.value = index;
};

// 获取轮播图数据
const fetchAdvertiseData = async () => {
  try {
    isLoading.value = true;
    
    // 根据position参数获取对应位置的广告数据
    const res = await getAdvertiseList(props.position);
    console.log(`获取到${props.position}位置的轮播列表:`, res);

    // 检查响应数据结构
    if (!res || !Array.isArray(res) || res.length === 0) {
      swipeImages.value = [];
      return;
    }

    // 将API返回的数据转换为正确的AdvertiseItem格式
    const listData = res.map((item: any) => ({
      id: typeof item.id === "number" ? item.id : Number(item.id) || 0,
      name: item.name || "",
      linkurl: item.linkurl || "",
      thumbnail: item.thumbnail || item.url || "", // 确保thumbnail存在
    }));

    swipeImages.value = [...listData];

    // 调试信息：输出轮播图数据和链接
    console.log(`${props.position}位置轮播图数据:`, swipeImages.value);
    swipeImages.value.forEach((item, index) => {
      console.log(`轮播图${index + 1}: ${item.name} -> ${item.linkurl}`);
      console.log(`处理后的图片URL: ${getFullImageUrl(item.thumbnail)}`);
    });

    // 预加载图片
    preloadImages(swipeImages.value);
  } catch (error) {
    console.error(`获取${props.position}位置的广告数据失败:`, error);
    showToast("获取数据失败，请稍后重试");
  } finally {
    isLoading.value = false;
  }
};

/**
 * 处理轮播图点击事件 - 智能链接处理
 * 支持的链接格式：
 * 1. 完整项目URL: https://houma.sxaliyun.cn/#/tcm-knowledge-detail/3
 * 2. 外部链接: https://www.baidu.com
 * 3. 内部路由: /tcm-knowledge-detail/3
 * 4. 相对路径: tcm-knowledge-detail/3 (会自动加上/)
 */
// 处理卡片点击
const handleItemClick = (item: AdvertiseItem) => {
  if (!item.linkurl) {
    showToast("链接地址不存在");
    return;
  }

  console.log("轮播图点击链接:", item.linkurl);
  
  // 触发自定义点击事件，允许父组件处理导航
  emits("click", item);
  
  // 智能链接处理
  try {
    const linkUrl = item.linkurl.trim();

    // 空链接检查
    if (!linkUrl) {
      showToast("链接地址为空");
      return;
    }

    // 1. 检查是否是完整的项目URL（包含域名和Hash路由）
    if (linkUrl.includes("#/")) {
      // 提取Hash后面的路由路径
      const hashIndex = linkUrl.indexOf("#/");
      const routePath = linkUrl.substring(hashIndex + 1); // 去掉#，保留/
      console.log("提取的路由路径:", routePath);

      // 验证提取的路径是否有效
      if (routePath && routePath.length > 1) {
        router.push(routePath);
      } else {
        showToast("无效的路由路径");
      }
      return;
    }

    // 2. 检查是否是外部链接（http/https开头但不包含项目Hash路由）
    if (
      (linkUrl.startsWith("http://") || linkUrl.startsWith("https://")) &&
      !linkUrl.includes("#/")
    ) {
      // 外部链接在新窗口打开
      window.open(linkUrl, "_blank");
      return;
    }

    // 3. 检查是否是项目内部路由路径（以/开头）
    if (linkUrl.startsWith("/")) {
      router.push(linkUrl);
      return;
    }

    // 4. 其他情况，尝试作为相对路径处理（自动添加/前缀）
    const relativePath = `/${linkUrl}`;
    console.log("作为相对路径处理:", relativePath);
    router.push(relativePath);
  } catch (error) {
    console.error("轮播图链接跳转失败:", error);
    showToast("链接跳转失败，请联系管理员检查链接格式");
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchAdvertiseData();
});

// 监听position变化，重新获取数据
watch(
  () => props.position,
  (newPosition, oldPosition) => {
    if (newPosition !== oldPosition) {
      fetchAdvertiseData();
    }
  },
  { immediate: false }
);
</script>

<style scoped>
/* 区块通用样式 */
.section-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 轮播图样式 */
.swipe-container {
  position: relative;
  width: 100%;
  padding: 0;
  box-shadow: 0 4px 15px rgba(75, 139, 244, 0.2);
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.swipe-container:hover {
  transform: scale(1.02);
}

.my-swipe {
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.swipe-item-content {
  position: relative;
  height: 100%;
}

.swipe-image {
  width: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 0;
}

.swipe-item-content:hover .swipe-image {
  transform: scale(1.05);
}

.swipe-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: #fff;
  padding: 20px 16px;
  font-size: 16px;
  text-align: left;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.custom-indicator {
  position: absolute;
  bottom: 15px;
  right: 15px;
  display: flex;
  gap: 8px;
}

.indicator-dot {
  width: 10px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  width: 20px;
  background-color: #fff;
  border-radius: 2px;
}

/* 骨架屏样式 */
.skeleton-swipe {
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.skeleton-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  animation: fadeIn 1.5s infinite;
}

.skeleton-item:nth-child(1) {
  animation-delay: 0s;
}

.skeleton-item:nth-child(2) {
  animation-delay: 0.5s;
}

.skeleton-item:nth-child(3) {
  animation-delay: 1s;
}

.skeleton-image {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
}

.skeleton-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(
    to top,
    rgba(224, 224, 224, 0.8),
    rgba(224, 224, 224, 0)
  );
}

@keyframes fadeIn {
  0%,
  100% {
    opacity: 0;
  }
  33%,
  66% {
    opacity: 1;
  }
}

/* 无数据状态 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #f8f8f8;
  border-radius: 12px;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

/* 图片加载优化 */
.swipe-image {
  width: 100%;
  object-fit: cover;
  transition: transform 0.3s ease, opacity 0.3s ease;
  border-radius: 0;
  background-color: #f5f5f5; /* 图片加载前的背景色 */
}

/* 图片加载动画 */
@keyframes imageLoad {
  from {
    opacity: 0;
    transform: scale(1.05);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.swipe-image {
  animation: imageLoad 0.5s ease-out;
}
</style>
