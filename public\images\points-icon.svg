<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700"/>
      <stop offset="50%" style="stop-color:#ffed4e"/>
      <stop offset="100%" style="stop-color:#ff9500"/>
    </linearGradient>
    <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9500;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#ff6b00;stop-opacity:0.1"/>
    </linearGradient>
  </defs>
  
  <!-- 阴影 -->
  <ellipse cx="32" cy="58" rx="28" ry="4" fill="url(#shadowGradient)"/>
  
  <!-- 金币主体 -->
  <circle cx="32" cy="32" r="28" fill="url(#coinGradient)" stroke="#ff9500" stroke-width="2"/>
  
  <!-- 内圆装饰 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="#ff9500" stroke-width="1" opacity="0.5"/>
  
  <!-- 中心图案 - 中医符号 -->
  <g transform="translate(32, 32)">
    <!-- 太极图案 -->
    <circle cx="0" cy="0" r="12" fill="#ff9500"/>
    <path d="M 0,-12 A 6,6 0 0,1 0,0 A 6,6 0 0,0 0,12 A 12,12 0 0,1 0,-12" fill="#ffd700"/>
    <circle cx="0" cy="-6" r="2" fill="#ffd700"/>
    <circle cx="0" cy="6" r="2" fill="#ff9500"/>
  </g>
  
  <!-- 光泽效果 -->
  <ellipse cx="24" cy="20" rx="8" ry="12" fill="white" opacity="0.3" transform="rotate(-30 24 20)"/>
  
  <!-- 装饰性星星 -->
  <g transform="translate(48, 16) scale(0.5)" opacity="0.8">
    <path d="M0,-6 L2,0 L6,0 L3,3 L4,8 L0,5 L-4,8 L-3,3 L-6,0 L-2,0 Z" fill="#ffd700"/>
  </g>
  
  <g transform="translate(16, 48) scale(0.3)" opacity="0.6">
    <path d="M0,-6 L2,0 L6,0 L3,3 L4,8 L0,5 L-4,8 L-3,3 L-6,0 L-2,0 Z" fill="#ffed4e"/>
  </g>
</svg>
