<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b"/>
      <stop offset="100%" style="stop-color:#ee5a52"/>
    </linearGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0"/>
    </radialGradient>
  </defs>
  
  <!-- 光晕效果 -->
  <circle cx="32" cy="32" r="30" fill="url(#glowGradient)"/>
  
  <!-- 心形主体 -->
  <path d="M32 52 C28 48 12 36 12 24 C12 18 16 14 22 14 C26 14 30 16 32 20 C34 16 38 14 42 14 C48 14 52 18 52 24 C52 36 36 48 32 52 Z" 
        fill="url(#heartGradient)" 
        stroke="#ee5a52" 
        stroke-width="1"/>
  
  <!-- 高光效果 -->
  <ellipse cx="26" cy="22" rx="4" ry="6" fill="white" opacity="0.4" transform="rotate(-20 26 22)"/>
  
  <!-- 装饰性小心形 -->
  <g transform="translate(45, 18) scale(0.3)" opacity="0.8">
    <path d="M0 12 C-2 10 -6 6 -6 2 C-6 0 -5 -1 -3 -1 C-2 -1 -1 0 0 1 C1 0 2 -1 3 -1 C5 -1 6 0 6 2 C6 6 2 10 0 12 Z" 
          fill="#ffb3ba"/>
  </g>
  
  <g transform="translate(19, 45) scale(0.25)" opacity="0.6">
    <path d="M0 12 C-2 10 -6 6 -6 2 C-6 0 -5 -1 -3 -1 C-2 -1 -1 0 0 1 C1 0 2 -1 3 -1 C5 -1 6 0 6 2 C6 6 2 10 0 12 Z" 
          fill="#ffb3ba"/>
  </g>
  
  <!-- 闪烁星星 -->
  <g transform="translate(48, 32) scale(0.4)" opacity="0.7">
    <path d="M0,-6 L1.5,0 L6,0 L2.5,2.5 L3.5,7 L0,4.5 L-3.5,7 L-2.5,2.5 L-6,0 L-1.5,0 Z" fill="#ffd700"/>
  </g>
  
  <g transform="translate(16, 28) scale(0.3)" opacity="0.5">
    <path d="M0,-6 L1.5,0 L6,0 L2.5,2.5 L3.5,7 L0,4.5 L-3.5,7 L-2.5,2.5 L-6,0 L-1.5,0 Z" fill="#ffd700"/>
  </g>
</svg>
