/**
 * 系统配置API模块
 * 提供医院信息、联系方式、SEO配置等API调用
 */
import { get } from './request'; // 使用封装好的get函数
import { SYSTEM_CONFIG_URLS } from './urls';
/**
 * 获取医院基本信息
 * @returns Promise<HospitalInfo | null> 成功返回医院信息，失败返回null
 */
export async function getHospitalInfo() {
    try {
        console.log('正在请求医院信息:', SYSTEM_CONFIG_URLS.HOSPITAL_INFO);
        const response = await get(SYSTEM_CONFIG_URLS.HOSPITAL_INFO);
        console.log('医院信息API响应:', response);
        // 新的分页格式：从results数组中取第一个元素
        if (response?.results && response.results.length > 0) {
            console.log('医院信息获取成功:', response.results[0]);
            return response.results[0];
        }
        console.warn('获取医院信息失败，使用默认配置');
        return null;
    }
    catch (error) {
        // 静默处理错误，不影响用户体验
        console.warn('医院信息API请求失败:', error);
        return null;
    }
}
/**
 * 获取联系方式信息
 * @returns Promise<ContactInfo | null> 成功返回联系信息，失败返回null
 */
export async function getContactInfo() {
    try {
        console.log('正在请求联系信息:', SYSTEM_CONFIG_URLS.CONTACT_INFO);
        const response = await get(SYSTEM_CONFIG_URLS.CONTACT_INFO);
        console.log('联系信息API响应:', response);
        // 新的分页格式：从results数组中取第一个元素
        if (response?.results && response.results.length > 0) {
            console.log('联系信息获取成功:', response.results[0]);
            return response.results[0];
        }
        console.warn('获取联系信息失败，使用默认配置');
        return null;
    }
    catch (error) {
        // 静默处理错误，不影响用户体验
        console.warn('联系信息API请求失败:', error);
        return null;
    }
}
/**
 * 获取SEO配置信息
 * @returns Promise<SeoConfig | null> 成功返回SEO配置，失败返回null
 */
export async function getSeoConfig() {
    try {
        console.log('正在请求SEO配置:', SYSTEM_CONFIG_URLS.SEO_CONFIG);
        const response = await get(SYSTEM_CONFIG_URLS.SEO_CONFIG);
        console.log('SEO配置API响应:', response);
        // 新的分页格式：从results数组中取第一个元素
        if (response?.results && response.results.length > 0) {
            console.log('SEO配置获取成功:', response.results[0]);
            return response.results[0];
        }
        console.warn('获取SEO配置失败，使用默认配置');
        return null;
    }
    catch (error) {
        // 静默处理错误，不影响用户体验
        console.warn('SEO配置API请求失败:', error);
        return null;
    }
}
/**
 * 获取所有系统配置
 * @returns Promise<SystemConfig> 返回包含所有配置的对象
 */
export async function getAllSystemConfig() {
    console.log('开始并行加载所有系统配置...');
    // 并行请求所有配置
    const [hospitalInfo, contactInfo, seoConfig] = await Promise.all([
        getHospitalInfo(),
        getContactInfo(),
        getSeoConfig()
    ]);
    console.log('系统配置加载完成:', {
        hospitalInfo,
        contactInfo,
        seoConfig
    });
    return {
        hospitalInfo,
        contactInfo,
        seoConfig
    };
}
