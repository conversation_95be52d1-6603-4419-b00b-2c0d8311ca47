import { get, requestWithRetry } from '../../api/request';
import { EDUCATION_MEDIA_URLS, buildUrl } from '../../api/urls';
/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getEducationDetail(id) {
    return get(buildUrl(EDUCATION_MEDIA_URLS.EDUCATION, id));
}
/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getEducationDetailWithRetry(id) {
    return requestWithRetry(() => getEducationDetail(id));
}
