<script setup lang="ts">
import { ref, onMounted } from "vue";
import axiosInstance from "./api"; // 引入封装的axios
import "vant/lib/index.css"; // 引入Vant样式
import "animate.css"; // 引入Animate.css



// 删除未使用的变量 message
const apiData = ref(null);
// const showAnimate = ref(false); // Removed unused variable

const fetchData = async () => {
  try {
    // 模拟API请求，实际项目中替换为您的后端接口
    const response = await axiosInstance.get("/some-data");
    apiData.value = response.data;
    console.log("API Data:", apiData.value);
  } catch (error) {
    console.error("Error fetching data:", error);
  }
};

// 删除未使用的函数 toggleAnimate



onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="container">
    <!-- 路由视图，用于渲染当前路由对应的组件 -->
    <router-view />

    <!-- 底部导航栏 -->
    <van-tabbar route fixed placeholder class="custom-tabbar">
      <van-tabbar-item replace to="/home" icon="home-o" class="tabbar-item">
        <template #icon="{ active }">
          <div class="icon-wrapper" :class="{ active }">
            <van-icon name="home-o" />
          </div>
        </template>
        <span>医院</span>
      </van-tabbar-item>
      <van-tabbar-item replace to="/knowledge" icon="bulb-o" class="tabbar-item">
        <template #icon="{ active }">
          <div class="icon-wrapper" :class="{ active }">
            <van-icon name="bulb-o" />
          </div>
        </template>
        <span>中医</span>
      </van-tabbar-item>
      <van-tabbar-item replace to="/activity" icon="fire-o" class="tabbar-item">
        <template #icon="{ active }">
          <div class="icon-wrapper" :class="{ active }">
            <van-icon name="fire-o" />
          </div>
        </template>
        <span>活动</span>
      </van-tabbar-item>
      <!-- 新增：我的页面 -->
      <van-tabbar-item replace to="/my" icon="user-o" class="tabbar-item">
        <template #icon="{ active }">
          <div class="icon-wrapper" :class="{ active }">
            <van-icon name="user-o" />
          </div>
        </template>
        <span>我的</span>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.container {
  /* padding-bottom: 50px; /* 为底部导航栏留出空间 */
  /* 这里的padding-bottom应该由每个页面自己控制，因为App.vue现在只负责路由视图和底部导航 */
  position: relative; /* 确保容器有相对定位 */
  width: 100%;
  min-height: 100vh;
}

/* 自定义底部导航栏样式 */
.custom-tabbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  height: 60px !important; /* 固定高度，防止变形 */
  position: fixed !important; /* 强制固定定位 */
  bottom: 0 !important; /* 固定在底部 */
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important; /* 确保在最顶层 */
}

/* 导航项样式 */
:deep(.van-tabbar-item) {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 0; /* 移除导航项的圆角 */
  margin: 2px 4px;
  position: relative;
  overflow: visible; /* 允许圆形效果超出 */
  height: 56px !important; /* 固定导航项高度 */
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  background: transparent !important; /* 移除背景 */
}

/* 移除选中状态的大面积背景 */
:deep(.van-tabbar-item--active) {
  background: transparent !important; /* 移除背景 */
  color: #4b8bf4 !important; /* 改为蓝色文字 */
  transform: none; /* 移除上移效果 */
  box-shadow: none; /* 移除阴影 */
}

/* 选中状态的文字颜色 */
:deep(.van-tabbar-item--active .van-tabbar-item__text) {
  color: #4b8bf4 !important;
  font-weight: 600;
}

/* 图标容器样式 */
.icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 16px; /* 圆形 */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  margin-bottom: 2px;
  background: transparent; /* 默认透明背景 */
}

/* 选中状态的图标容器 - 小圆形效果 */
.icon-wrapper.active {
  background: linear-gradient(135deg, #4b8bf4 0%, #667eea 100%);
  transform: scale(1.0); /* 不放大 */
  box-shadow: 0 2px 8px rgba(75, 139, 244, 0.3);
}

/* 图标样式 */
.icon-wrapper .van-icon {
  font-size: 18px;
  transition: all 0.3s ease;
  color: #666; /* 默认灰色 */
}

/* 选中状态的图标 */
.icon-wrapper.active .van-icon {
  color: #fff !important; /* 选中时白色图标 */
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* 文字样式 */
:deep(.van-tabbar-item__text) {
  font-size: 11px;
  margin-top: 1px;
  transition: all 0.3s ease;
  font-weight: 500;
  line-height: 1;
  color: #666; /* 默认灰色 */
}

/* 未选中状态的样式 */
:deep(.van-tabbar-item:not(.van-tabbar-item--active)) {
  color: #666;
}

/* 移除悬停效果，简化交互 */
:deep(.van-tabbar-item:not(.van-tabbar-item--active):hover) {
  background: transparent;
  transform: none;
}

/* 悬停时图标容器的效果 */
:deep(.van-tabbar-item:not(.van-tabbar-item--active):hover) .icon-wrapper {
  background: rgba(75, 139, 244, 0.1);
  transform: scale(1.05);
}

/* 移除顶部指示器 */
:deep(.van-tabbar-item--active)::before {
  display: none;
}

/* 移除波纹效果 */
:deep(.van-tabbar-item)::after {
  display: none;
}

/* Vant组件内部样式覆盖 */
:deep(.van-tabbar-item__icon) {
  margin-bottom: 1px !important;
}

/* 添加一个小圆点指示器（可选） */
:deep(.van-tabbar-item--active)::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #4b8bf4;
  border-radius: 50%;
  animation: dotFadeIn 0.3s ease;
}

@keyframes dotFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 350px) {
  .icon-wrapper {
    width: 28px;
    height: 28px;
    border-radius: 14px;
  }
  
  .icon-wrapper .van-icon {
    font-size: 16px;
  }
  
  :deep(.van-tabbar-item__text) {
    font-size: 10px;
  }
  
  .custom-tabbar {
    height: 55px !important;
  }
  
  :deep(.van-tabbar-item) {
    height: 51px !important;
  }
}

/* 确保整体高度一致性 */
:deep(.van-tabbar--fixed) {
  height: 60px !important;
  position: fixed !important; /* 强制固定定位 */
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
}

/* 强制覆盖任何可能的位置样式 */
:deep(.van-tabbar) {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
}
</style>
